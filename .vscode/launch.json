{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Client",
      "url": "http://localhost:3000/",
      "webRoot": "${workspaceFolder}/tytz-vue/src",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        // "webpack:///*": "${webRoot}/*"
        "../*": "${webRoot}/*"
      },
      "skipFiles": ["node_modules/**"]
    },
    {
      "type": "java",
      "name": "Server",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "mainClass": "com.youlai.boot.YouLaiBootApplication",
      "projectName": "youlai-boot",
      "args": "",
      "envFile": "${workspaceFolder}/.env"
    }
  ],
  "compounds": [
    {
      "name": "Server & Client",
      "configurations": ["Server", "Client"],
      "preLaunchTask": "Start Debug",
      "stopAll": true
    }
  ]
}
