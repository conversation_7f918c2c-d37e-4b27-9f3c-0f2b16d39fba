import request from "@/utils/request";

const API_PREFIX = "/api/v1/appointments";
//const API_PREFIX = "/prod-api/api/v1/appointments";

/**
 * 约见管理分页查询对象
 */
export interface appointmentsPageQuery extends PageQuery {
  /** 约见人名称 */
  appointmentName?: string;
  /** 约见状态 */
  handleStatus?: string;
  /** 开始时间 */
  startTimeBegin?: string;
  /** 结束时间 */
  startTimeEnd?: string;
}
/**获取新闻资讯分页列表 */
export interface appointmentsPageVO {
  /** 主键 */
  id: string;

  /** 约见人名称 */
  appointmentName?: string;

  /** 所属单位 */
  appointmentUnit?: string;

  /** 联系方式 */
  appointmentContact?: string;

  /** 被约见部委 */
  appointmentDepartment?: string;

  /** 约见原因*/
  appointmentReason?: string;

  /** 约见开始时间*/
  appointmentStartTime?: string;

  /** 约见结束时间*/
  appointmentEndTime?: string;

  /** 处理状态*/
  handleStatus?: string;

  /** 约见状态*/
  appointmentStatus?: string;

  /** 处理意见*/
  handleComment?: string;

  /** 意见反馈*/
  feedback: string;

  /**创建时间 */
  createTime?: string;
}
/**约见管理分页列表 */
export const appointmentPageApi = (queryParams: appointmentsPageQuery) => {
  if (!queryParams.startTimeBegin) {
    delete queryParams.startTimeBegin;
  }
  if (!queryParams.startTimeEnd) {
    delete queryParams.startTimeEnd;
  }
  return request<any, PageResult<appointmentsPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取处理意见表单数据*/
export const appointmentViewApi = (id: string) => {
  return request<any, appointmentsPageVO>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 处理约见申请*/
export const handleAppointmentApi = (id: number, data: appointmentsPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}/handle`,
    method: "put",
    data: data,
  });
};

/** 处理意见反馈*/
export const feedbackApi = (id: string, data: appointmentsPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}/feedback`,
    method: "put",
    data: data,
  });
};

/**导出 */
export const feedbackExportApi = (queryParams: any) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/export`,
    method: "post",
    data: queryParams,
    responseType: "blob",
  });
};

/** 获取约见信息列表 */
export const getAppointmentListApi = (status: string) => {
  return request<any, any>({
    url: `${API_PREFIX}/status/${status}`,
    method: "get",
  });
};

/** 删除约见申请 */
export const appointmentDeleteApi = (id: string) => {
  return appointmentBatchDeleteApi({ ids: [id] });
};

/** 批量删除约见申请 */
export const appointmentBatchDeleteApi = (params: { ids: string[] }) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${params.ids.join(",")}`,
    method: "delete",
  });
};
