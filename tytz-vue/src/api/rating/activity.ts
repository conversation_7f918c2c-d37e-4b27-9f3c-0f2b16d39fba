import request from "@/utils/request";
import { IActivityDetail } from "@/api/rating/types/activity";

const API_PREFIX = "/api/v1/activities";

/**
 * 活动信息分页查询对象
 */
export interface activityPageQuery extends PageQuery {
  /** 活动名称 */
  title?: string;
  /** 活动类型 */
  activityType?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**获取活动信息分页列表 */
export interface activityPageVO {
  /** 主键 */
  id: string;

  /** 活动名称 */
  title?: string;

  /** 活动类型 */
  activityType?: string;

  /** 活动归类（所属商会） */
  category?: string;

  /** 主办单位 */
  department?: string;

  /** 参与人员JSON数据 */
  participants?: any;

  activityTime?: [string, string];

  /** 活动开始时间*/
  startTime: string;

  /** 活动结束时间*/
  endTime: string;

  /** 活动内容 */
  content?: string;

  /** 附件JSON数据*/
  attachments?: string;

  /** 创建时间 */
  createTime?: string;
}
/**活动信息分页列表 */
export const activityPageApi = (queryParams: activityPageQuery) => {
  if (!queryParams.startTime) {
    delete queryParams.startTime;
  }
  if (!queryParams.endTime) {
    delete queryParams.endTime;
  }
  return request<any, PageResult<activityPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取活动信息表单数据*/
export const activityViewApi = (id: string) => {
  return request<any, IActivityDetail>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 新增活动信息*/
export const activityAddApi = (data: activityPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}`,
    method: "post",
    data: data,
  });
};

/** 修改活动信息*/
export const activityUpdateApi = (id: string, data: activityPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "put",
    data: data,
  });
};

/** 删除活动信息*/
export const activityDeleteApi = (id: string) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "delete",
  });
};
