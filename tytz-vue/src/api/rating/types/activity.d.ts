/** 活动详情表单对象 */
export interface IActivityDetail {
  /**
   * 活动类型
   */
  activityType: string;
  //   activityType: ActivityTypeEnum;

  /** 活动归类 */
  category: ScoringCategoryEnum[];

  /**
   * 附件
   */
  attachments?: { name: string; url: string }[];
  /**
   * 活动内容
   */
  content: string;
  /**
   * 活动结束时间
   */
  endTime: string;
  /**
   * id
   */
  id: string;
  /**
   * 参与人员ID列表
   */
  participants: { id: string; businessName: string; businessMember?: string }[];
  /**
   * 活动开始时间
   */
  startTime: string;
  /**
   * 活动名称
   */
  title: string;
}
