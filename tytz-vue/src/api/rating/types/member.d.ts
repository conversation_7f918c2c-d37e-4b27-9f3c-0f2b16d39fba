/** 商会会员列表对象 */
export interface ChamberOfCommerceMemberVO {
  /** 会员ID */
  id: string;

  /** 会员名称 */
  username: string;

  /** 会员昵称 */
  nickname: string;

  /** 会员手机号 */
  mobile: string;

  /** 会员性别 */
  gender: number;

  /** 会员头像 */
  avatar: string;

  /** 会员邮箱 */
  email: string;

  /** 会员状态 */
  status: number;

  /** 会员所属部门名称，多个部门名称使用英文逗号(,)分割 */
  deptName?: string;

  /** 会员角色列表 */
  roles: RoleInfo[];

  /** 职务 */
  company: string;
}

/** 商会会员角色信息 */
export interface RoleInfo {
  /** 角色ID */
  roleId: string;

  /** 角色名称 */
  roleName: string;

  /** 角色编码 */
  roleCode: string;
}
