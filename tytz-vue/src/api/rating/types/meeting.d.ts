/** 会议详情表单对象 */
export interface IMeetingDetail {
  /**
   * 附件
   */
  attachments?: { name: string; url: string }[];
  /**
   * 会议归类
   */
  category: ScoringCategoryEnum[];
  /**
   * 会议内容
   */
  content: string;
  /**
   * 会议结束时间
   */
  endTime: string;
  /**
   * id
   */
  id: number;
  /**
   * 会议类型
   */
  meetingType: MeetingType;
  /**
   * 参会人员
   */
  participants: { id: number; businessName: string; businessMember?: string }[];
  /**
   * 会议开始时间
   */
  startTime: string;
  /**
   * 会议名称
   */
  title: string;
}
