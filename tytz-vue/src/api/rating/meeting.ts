import request from "@/utils/request";
import { IMeetingDetail } from "./types/meeting";

const API_PREFIX = "/api/v1/meetings";

/**
 * 会议信息分页查询对象
 */
export interface meetingPageQuery extends PageQuery {
  /** 会议名称 */
  title?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**获取会议信息分页列表 */
export interface meetingPageVO {
  /** 主键 */
  id: string;

  /** 会议名称 */
  title?: string;

  /** 会议类型 */
  meetingType?: string;

  /** 活动归类（所属商会） */
  category?: string;

  /** 组织者 */
  department?: string;

  /** 参与人员JSON数据 */
  participants?: any;

  meetingTime?: [string, string];

  /** 开始时间*/
  startTime: string;

  /** 结束时间*/
  endTime: string;

  /** 内容 */
  content?: string;

  /** 附件JSON数据*/
  attachments?: string;

  /** 创建时间 */
  createTime?: string;
}
/** 会议信息分页列表 */
export const meetingPageApi = (queryParams: meetingPageQuery) => {
  if (!queryParams.startTime) {
    delete queryParams.startTime;
  }
  if (!queryParams.endTime) {
    delete queryParams.endTime;
  }
  return request<any, PageResult<meetingPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取会议信息表单数据*/
export const meetingViewApi = (id: string) => {
  return request<any, IMeetingDetail>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 新增会议信息*/
export const meetingAddApi = (data: meetingPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}`,
    method: "post",
    data: data,
  });
};

/** 修改会议信息*/
export const meetingUpdateApi = (id: string, data: meetingPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "put",
    data: data,
  });
};

/** 删除会议信息*/
export const meetingDeleteApi = (id: string) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "delete",
  });
};
