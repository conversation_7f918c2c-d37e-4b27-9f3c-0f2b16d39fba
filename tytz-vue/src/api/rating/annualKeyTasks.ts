import request from "@/utils/request";
import { IAnnualKeyTasksDetail } from "./types/annualKeyTasks";

const API_PREFIX = "/api/v1/works";

/**
 * 年度重点工作分页查询对象
 */
export interface annualKeyTasksPageQuery extends PageQuery {
  /** 年度 */
  year?: string;
  /** 工作名称 */
  workName?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**获取新闻资讯分页列表 */
export interface annualKeyTasksPageVO {
  /** 主键 */
  id: string;

  /** 年度 */
  year?: string;

  /** 工作名称 */
  workName?: string;

  /** 工作类型 */
  workType?: string;

  /** 工作归类 */
  category?: string;

  /** 负责部门 */
  department?: string;

  /** 参与人员JSON数据 */
  participants?: any;

  participationTime?: [string, string];

  /** 开始时间*/
  startTime: string;

  /** 结束时间*/
  endTime: string;

  /** 工作内容 */
  workContent?: string;

  /** 附件JSON数据*/
  attachments?: string;

  /** 创建时间 */
  createTime?: string;
}
/**新闻资讯分页列表 */
export const annualKeyTasksPageApi = (queryParams: annualKeyTasksPageQuery) => {
  if (!queryParams.startTime) {
    delete queryParams.startTime;
  }
  if (!queryParams.endTime) {
    delete queryParams.endTime;
  }
  return request<any, PageResult<annualKeyTasksPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取新闻资讯表单数据*/
export const annualKeyTasksViewApi = (id: string) => {
  return request<any, IAnnualKeyTasksDetail>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 新增新闻资讯*/
export const annualKeyTasksAddApi = (data: annualKeyTasksPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}`,
    method: "post",
    data: data,
  });
};

/** 修改新闻资讯*/
export const annualKeyTasksUpdateApi = (id: string, data: annualKeyTasksPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "put",
    data: data,
  });
};

/** 删除新闻资讯*/
export const annualKeyTasksDeleteApi = (id: string) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "delete",
  });
};
