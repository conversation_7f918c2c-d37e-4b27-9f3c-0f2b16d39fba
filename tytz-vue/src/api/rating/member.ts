import request from "@/utils/request";
import { ChamberOfCommerceMemberVO } from "./types/member";

const API_PREFIX = "/api/v1/coc-members";

/**
 * @description 获取商会会员列表（包含总商会和新联会）
 */
export const getChamberOfCommerceMembers = (queryParams: {
  memberName?: string;
  chamberOfCommerceCodes?: string[];
}) => {
  return request<any, ChamberOfCommerceMemberVO[]>({
    url: `${API_PREFIX}/list`,
    method: "post",
    data: queryParams,
  });
};
