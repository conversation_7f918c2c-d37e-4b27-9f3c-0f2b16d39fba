import { dictsToMap } from "@/utils/dicts";

/**
 * 履职得分归类枚举
 */
export enum ScoringCategoryEnum {
  /** 总商会 */
  ZSH = "SHCY",
  /** 新联会 */
  XLH = "XLHHY",
}

/**
 * 履职得分归类字典数据
 */
export const ScoringCategoryDicts: Dict<ScoringCategoryEnum>[] = [
  { value: ScoringCategoryEnum.ZSH, label: "总商会", sort: 1 },
  { value: ScoringCategoryEnum.XLH, label: "新联会", sort: 2 },
];

/**
 * 履职得分归类字典数据Map
 */
export const ScoringCategoryDictMap = dictsToMap(ScoringCategoryDicts);
