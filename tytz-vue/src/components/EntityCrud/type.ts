// import { ComponentExposed } from "vue-component-type-helpers";

import { FormItemRule } from "element-plus";

/**
 * 筛选表单项定义
 * 用于定义筛选表单中的每个字段的配置
 */
export type FilterFormBaseItem = {
  /** 表单项标签文本 */
  label: string;
  /** 表单项占位提示文本 */
  placeholder?: string;
};

/** 筛选表单无属性项，不需要配置的属性的项目*/
export type FilterFormNonPropItem = FilterFormBaseItem & {
  type: "input" | "date";
};

/** 筛选表单下拉选择项 */
export type FilterFormSelectItem = FilterFormBaseItem & {
  type: "select";
  /** 下拉选项列表 */
  options: { value: string; label: string }[];
};

/** 筛选表单单选选择项 */
export type FilterFormRadioItem = FilterFormBaseItem & {
  type: "radio";
  initialValue: string;
  /** 下拉选项列表 */
  options: { value: string; label: string }[];
};

/** 筛选表单远程下拉选择项 */
export type FilterFormSelectRemoteItem = FilterFormBaseItem & {
  type: "select-remote";
  /** 远程搜索时执行的函数 */
  remoteFetchService: (query: string) => Promise<{ value: string; label: string }[]>;
};

/** 筛选表单日期范围项 */
export type FilterFormDateRangeItem = FilterFormBaseItem & {
  type: "date-range";
  /** 日期选择器配置时两个字段名 */
  dateRangeFields: [string, string];
};

/** 筛选表单项联合类型 */
export type FilterFormUnionItem =
  | FilterFormNonPropItem
  | FilterFormSelectItem
  | FilterFormSelectRemoteItem
  | FilterFormDateRangeItem
  | FilterFormRadioItem;

/** 筛选表单项集合,key 为表单项字段名 */
export type FilterFormItems = Record<string, FilterFormUnionItem>;

/** 内部操作类型，用于覆盖默认的内部操作 */
export type InternalOperationType =
  | "create"
  | "detail"
  | "update"
  | "delete"
  | "publish"
  | "unpublish"
  | "approve"
  | "reject"
  | "batchDelete"
  | "batchPublish"
  | "batchApprove"
  | "top"
  | "untop";

/**
 * 可编辑表单项定义
 * 用于定义新增/编辑表单中的每个字段的配置
 */
export type EditableFormItem = {
  /** 表单项标签文本 */
  label?: string;
  /** 是否必填 */
  required?: boolean;
  /** 必填校验失败时的提示文本 */
  requiredTip?: string;
  /** 表单项占位提示文本 */
  placeholder?: string;
  /** 表单项宽度 */
  width?: string;
  /**
   * 表单组件的prop
   * TODO: 需要做一下类型约束
   */
  // props?: ComponentTypeMap[K] & ComponentsMixinPropsType;
  props?: Record<string, any>;
  /** 自定义校验规则 */
  rules?: FormItemRule[];
};

/** 可编辑表单无属性项，不需要配置的属性的项目*/
export type EditableFormNonPropItem = EditableFormItem & {
  /** 输入类型 */
  /* 
    这里使用了 mobile, person-name 这种类型而不是使用 input 配合校验属性之类是让开发时更关注实际的业务类型而不是具体的实现
    这样更容易统一管理系统中各类型业务规则
    */
  type:
    | "id"
    | "input"
    | "number"
    | "non-negative-number" // 非负数字
    | "textarea"
    | "editor"
    | "date"
    | "mobile"
    | "tel"
    | "person-name"
    | "date-picker"
    | "radio";
};

export type EditableFormSlotItem = EditableFormItem & {
  type: "slot";
  /** 插槽是否覆盖整个表单项，默认为 false */
  slotOnFormItem?: boolean;
};

/** 可编辑表单远程下拉选择项 */
export type EditableFormRemoteSelectItem = EditableFormItem & {
  /** 输入类型 */
  type: "select-remote";
  /** 远程搜索时执行的函数 */
  remoteFetchService: (query: string) => Promise<{ value: string; label: string }[]>;
};

export type EditableFormRadioItem = EditableFormItem & {
  type: "radio"; // 指定类型为 radio
  initialValue?: string;
  options?: { label: string; value: string }[];
};

export type EditableFormSwitchItem = EditableFormItem & {
  type: "switch";
  /** 激活状态文本 */
  activeText?: string;
  /** 非激活状态文本 */
  inactiveText?: string;
  /** 激活状态值 */
  activeValue?: any;
  /** 非激活状态值 */
  inactiveValue?: any;
};

export type EditableFormSelectItem = EditableFormItem & {
  /** 输入类型 */
  type: "select";
  /** 下拉选项列表,仅在 type 为 select 时有效 */
  options?: { value: string | number; label: string }[];
};

export type EditableFormSingleUploadItem = EditableFormItem & {
  type: "single-image" | "single-document";
  /** 最大文件大小 MB */
  maxFileSize?: number;
  /** 文件类型 */
  acceptType?: string;
  /** 文件上传提示文本 */
  tip?: string | Array<string>;
};

export type EditableFormMultipleUploadItem = EditableFormItem & {
  type: "multiple-image" | "multiple-document";
  /** 最大文件数量 */
  maxFileCount?: number;
  /** 最大文件大小 MB */
  maxFileSize?: number;
  /** 文件类型 */
  acceptType?: string;
  /** 文件上传提示文本 */
  tip?: string;
};

/** 可编辑表单日期选择项 */
export type EditableFormDatePickerItem = EditableFormItem & {
  type: "date-picker";
  /** 日期类型 */
  dateType?: "date" | "datetime";
  /** 日期值 */
  value?: [string, string];
};

/** 可编辑表单项联合类型 */
export type EditableFormUnionType =
  | EditableFormNonPropItem
  | EditableFormSlotItem
  | EditableFormSelectItem
  | EditableFormSwitchItem
  | EditableFormSingleUploadItem
  | EditableFormMultipleUploadItem
  | EditableFormRemoteSelectItem
  | EditableFormDatePickerItem
  | EditableFormRadioItem;

/**
 * 可展示表单项定义
 * 用于定义详情页中的每个字段的配置
 */
export type DisplayableItem = {
  /** 表单项属性名 */
  prop: string;
  /** 表单项标签文本 */
  label: string;
  /** 表单项类型 */
  type: "text" | "rich-text" | "documents" | "images" | "date-range" | "radio";
  /** 表单项占位提示文本 */
  placeholder?: string;
};

/**
 * 表格列定义
 * 用于定义表格中每一列的展示配置
 */
export type TableColumnItem<TRecord> = {
  /** 列标题 */
  label: string;
  /** 列宽度 */
  width?: number | string;
  /** 最小列宽度 */
  minWidth?: number;
  /** 单元格内容格式化函数 */
  formatter?: (value: TRecord) => string;
  /** 空值时显示的文本 */
  emptyText?: string;
};

/**
 * 分页查询参数
 * 为查询参数扩展分页相关字段
 */
export type PageQuery<T> = T & {
  /** 当前页码 */
  pageNum: number;
  /** 每页条数 */
  pageSize: number;
};

/**
 * 分页查询结果
 * 包含数据列表和总条数
 */
export type PageQueryResult<TRecord> = {
  /** 数据列表 */
  list: TRecord[];
  /** 总条数 */
  total: number;
};

/**
 * 实体基础属性
 * 包含实体的基本信息
 */
export type EntityProps = {
  /** 实体名称,用于接口请求等 */
  entityName: string;
  /** 实体显示名称,用于界面展示 */
  displayName: string;
};

export type RowOperation<TRecord> = {
  /** 覆盖内部操作类型 */
  overwriteInternalType?: InternalOperationType;
  /** 操作类型 */
  type: "link" | "button"; // 这里不在允许实现 component 组件类型，如果碰到需要实现组件类型的情况，可以考虑使用 slot 实现
  /** 操作按钮文本 */
  label: string;
  /** 按钮样式计算函数 */
  colorize?: (params: TRecord) => string;
  /** 操作确认提示文本 */
  confirmMessage?: string;
  /** 操作执行函数 */
  actionService: (
    params: TRecord,
    /**
     * 如果当 type 为 internal 时，将在调用 actionService 的时候把内容的方法作为 internalService 参数传入
     * 如果你想实现你自己的方法，请忽略这个 internalService 参数
     */
    internalService?: (params: TRecord) => Promise<any>
  ) => Promise<any>;
  /** 是否显示 */
  canDisplay?: (params: TRecord) => boolean;
  displayIndex?: number;
};

/**
 * 实体列表属性
 * 包含列表页所需的所有配置
 */
export type EntityListProps<TRecord> = EntityProps & {
  /** 筛选表单配置 */
  filterFormItems?: FilterFormItems | any;
  /** 表格顶部操作按钮配置 */
  operations?: {
    /** 操作类型 */
    /** 内部操作类型，用于覆盖默认的内部操作 */
    overwriteInternalType?: InternalOperationType;
    /** internal 内部操作 */
    type: "button"; // 这里不在允许实现 component 组件类型，如果碰到需要实现特定的情况，可以考虑使用 slot 实现
    /** 操作按钮文本 */
    label: string;
    /** 按钮样式 */
    colorize: "primary" | "success" | "warning" | "danger" | "info";
    /** 操作执行函数 */
    actionService: (
      params: TRecord[],
      /**
       * 如果当 type 为 internal 时，将在调用 actionService 的时候把内容的方法作为 internalService 参数传入
       * 如果你想实现你自己的方法，请忽略这个 internalService 参数
       */
      internalService?: (params: TRecord[]) => Promise<any>
    ) => Promise<any>;
    displayIndex?: number;
  }[];
  /** 是否显示序号列 */
  hasIndexColumn?: boolean;
  /** 是否显示选择列 */
  hasSelectionColumn?: boolean;
  /** 表格列配置 可以直接省略为 列名: 列标题 */
  tableColumns: {
    [K in keyof TRecord | "operations"]?: TableColumnItem<TRecord> | string;
    // !!REVIEW
    // TODO 可能的问题: 'operations'字段类型未定义可能会引发错误
    // 修改建议：为'operations'字段明确指定类型
  };
  /** 行操作按钮配置 */
  rowOperations?: RowOperation<TRecord>[];
  /** 获取列表数据的服务函数 */
  listFetchService: (params: PageQuery<any>) => Promise<PageQueryResult<TRecord>>;
};

/**
 * 可编辑表单项配置
 * 包含可编辑表单项的配置
 */
export type EditableItem<T> = {
  [K in keyof T | "slot" | `slot_${string}`]?: string | EditableFormUnionType;
};

/**
 * 实体详情属性
 * 包含详情页所需的配置
 */
export type EntityDetailProps<TRecord, TDetail> = EntityProps & {
  /** 根据内容是否显示详情按钮 */
  canViewDetail?: (params: TRecord) => boolean;
  /** 是否使用创建表单的配置作为详情表单的配置 */
  useCreateFormItemsAsDetail?: boolean;
  /** 详情表单字段配置 */
  detailFormItems?: EditableItem<TDetail>;
  /** 详情按钮文本 */
  detailButtonLabel?: string;
  /** 获取详情数据的服务函数，如果使用 page 查回来的对象进行查看简单使用 (row) => Promise.resolve(row) 即可 */
  detailFetchService?: (row: TRecord) => Promise<TDetail>;
};

/**
 * 实体创建属性
 * 包含创建表单所需的配置
 */
export type EntityCreateProps<TCreate extends Record<string, any>> = EntityProps & {
  /** 创建表单字段配置 */
  createFormItems?: EditableItem<TCreate>;
  /** 创建按钮文本 */
  createButtonLabel?: string;
  /** 创建数据的服务函数 */
  createService?: (params: TCreate) => Promise<any>;
};

/**
 * 实体删除属性
 * 包含删除操作所需的配置
 */
export type EntityDeleteProps<TRecord> = EntityProps & {
  /** 根据内容是否显示删除按钮 */
  canDelete?: (params: TRecord) => boolean;
  /** 删除按钮文本 */
  deleteButtonLabel?: string;
  /** 删除单条数据的服务函数 */
  deleteService?: (params: TRecord) => Promise<any>;
  /** 批量删除按钮文本 */
  batchDeleteButtonLabel?: string;
  /** 批量删除数据的服务函数 */
  batchDeleteService?: (params: TRecord[]) => Promise<any>;
};

/**
 * 实体更新属性
 * 包含更新表单所需的配置
 */
export type EntityUpdateProps<
  TUpdate extends Record<string, any>,
  TUpdateableData extends Record<string, any>,
> = EntityProps & {
  /** 根据内容是否显示更新按钮 */
  canUpdate?: (params: TUpdate) => boolean;
  /** 是否使用创建表单的配置作为更新表单的配置 */
  useCreateFormItemsAsUpdate?: boolean;
  /** 更新表单字段配置 */
  updateFormItems?: EditableItem<TUpdate | TUpdateableData>;
  /** 更新按钮文本 */
  updateButtonLabel?: string;
  /** 获取更新前数据的服务函数，用于处理当前显示的数据不满足更新对话框的数据需求 */
  fetchUpdateableDataService?: (params: TUpdate) => Promise<TUpdateableData>;
  /** 更新数据的服务函数 */
  updateService?: (params: TUpdate | TUpdateableData) => Promise<any>;
};

/**
 * 实体发布属性
 * 包含发布/取消发布操作所需的配置
 */
export type EntityPublishProps<TRecord> = EntityProps & {
  /** 发布按钮文本 */
  publishButtonLabel?: string;
  /** 判断是否可以发布的函数 */
  canPublish?: (params: TRecord) => boolean;
  /** 发布操作的服务函数 */
  publishService?: (params: TRecord) => Promise<any>;
  /** 取消发布按钮文本 */
  unpublishButtonLabel?: string;
  /** 判断是否可以取消发布的函数 */
  canUnpublish?: (params: TRecord) => boolean;
  /** 取消发布操作的服务函数 */
  unpublishService?: (params: TRecord) => Promise<any>;
};

/**
 * 实体置顶属性
 * 包含置顶/取消置顶操作所需的配置
 */
export type EntityTopProps<TRecord> = EntityProps & {
  /** 置顶按钮文本 */
  topButtonLabel?: string;
  /** 判断是否可以置顶的函数 */
  canTop?: (params: TRecord) => boolean;
  /** 置顶操作的服务函数 */
  topService?: (params: TRecord) => Promise<any>;
  /** 取消置顶按钮文本 */
  untopButtonLabel?: string;
  /** 判断是否可以取消置顶的函数 */
  canUntop?: (params: TRecord) => boolean;
  /** 取消置顶操作的服务函数 */
  untopService?: (params: TRecord) => Promise<any>;
};

/**
 * 实体审核属性
 * 包含审核/驳回操作所需的配置
 */
export type EntityAuditProps<TRecord> = EntityProps & {
  /** 审核通过按钮文本 */
  approveButtonLabel?: string;
  /** 判断是否可以审核通过的函数 */
  canApprove?: (params: TRecord) => boolean;
  /** 审核通过操作的服务函数 */
  approveService?: (params: TRecord) => Promise<any>;
  /** 驳回按钮文本 */
  rejectButtonLabel?: string;
  /** 判断是否可以驳回的函数 */
  canReject?: (params: TRecord) => boolean;
  /** 驳回操作的服务函数 */
  rejectService?: (params: TRecord) => Promise<any>;
};

/**
 * 实体完整属性
 * 包含实体所有操作的完整配置
 */
export type EntityCrudProps<
  TEntity extends Record<string, any>,
  TDetail extends Record<string, any> = TEntity,
> = EntityListProps<TEntity> &
  EntityCreateProps<TEntity> &
  EntityUpdateProps<TEntity, TDetail> &
  EntityDetailProps<TEntity, TDetail> &
  EntityDeleteProps<TEntity> &
  EntityPublishProps<TEntity> &
  EntityAuditProps<TEntity> &
  EntityTopProps<TEntity>;

/** EntityCrud 组件实例类型 */
// export type EntityCrudInstance = ComponentExposed<typeof import("./index.vue").default>;
// HACK: 下面的写法是为了对 defineExpose 的方法支持泛型
export type EntityCrudInstance<TEntity extends Record<string, any> = Record<string, any>> = {
  /** 获取选中的行数据 */
  getSelectedRows: () => TEntity[];
  /** 获取当前表单数据 */
  getCurrentFormData: () => Record<string, any>;
  /** 刷新表格 */
  handleRefresh: () => void;
  /** 选中的行数据 */
  selectedRows: TEntity[];
};
