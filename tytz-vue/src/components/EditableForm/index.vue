<script setup lang="ts">
import { ElDialog, ElMessage, type FormInstance } from "element-plus";
import { cloneDeep, omit } from "lodash-es";
import { computed, reactive, useTemplateRef, watch } from "vue";

import CustomComponent from "@/components/CustomComponent/index.vue";
import { mergePropsAndAttrs } from "@/hooks/components";
import { getCssUnit } from "@/utils/css";

import { DIALOG_TYPE } from "./config";
import type { DialogPageProps } from "./type";
import { initPlaceholder, initRules } from "./util";

const props = withDefaults(defineProps<DialogPageProps<Record<string, any>>>(), {
  width: "500px",
  visible: false,
  type: DIALOG_TYPE.ADD,
});

const emit = defineEmits<{
  "update:visible": [visible: boolean];
  beforeSubmit: [data: any];
  success: [data: any];
  change: [Record<string, any>];
  submit: [Record<string, any>];
  refresh: [];
  cancel: [];
}>();

// Form组件相关逻辑
const formAttrs = omit(mergePropsAndAttrs(props), "title"); // 不要title
const items = computed(() => {
  return cloneDeep(props.formItems || [])
    .filter((item: any) => !item.hidden)
    .map((item: any) => {
      item.props || (item.props = {});
      Object.assign(item.props, {
        placeholder: initPlaceholder(item),
      });
      item.formItem.rules = initRules(item);
      item.width = getCssUnit(item.width || props.width);
      return item;
    });
});

const ElFormRef = useTemplateRef<FormInstance>("ElFormRef");
const formData = reactive<Record<string, any>>({});

// 监听visible变化，获取详情
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      // 清空表单
      Object.keys(formData).forEach((key) => {
        delete formData[key];
      });

      // 初始化表单默认值
      items.value.forEach((item: any) => {
        formData[item.formItem.prop] = item.initialValue;
      });

      // 如果有初始数据服务，则使用初始数据服务
      if (props.initialDataService && props.initialData) {
        try {
          const detail = await props.initialDataService(props.initialData);
          Object.assign(formData, detail);
        } catch (error) {
          console.error(error);
          cancelDialog();
        }
      } else if (props.initialData) {
        Object.assign(formData, props.initialData);
      } else {
        Object.assign(formData, {});
      }

      // 如果是编辑模式且有详情接口，则获取详情
      if (props.type === DIALOG_TYPE.EDIT && props.detailService && formData.id) {
        try {
          const detail = await props.detailService(formData.id);
          Object.assign(formData, detail);
        } catch (error) {
          console.error(error);
          cancelDialog();
        }
      }
    }
  }
);

function closeDialog() {
  Object.keys(formData).forEach((key) => {
    Object.assign(formData, {
      [key]: null,
    });
  });

  reset();
  emit("update:visible", false);
}

function successDialog() {
  emit("success", formData);
  closeDialog();
}

function cancelDialog() {
  emit("cancel");
  closeDialog();
}

function assign(key: string, value: any) {
  Object.assign(formData, {
    [key]: value,
  });
}

async function submit() {
  try {
    await ElFormRef.value?.validate();
    const formatData = Object.fromEntries(
      props.formItems.map((item: any) => [
        item.formItem.prop,
        item.formatter
          ? item.formatter(formData[item.formItem.prop])
          : formData[item.formItem.prop],
      ])
    );

    emit("beforeSubmit", formatData);

    // 如果配置了提交请求，则调用提交请求
    // 否则，直接关闭弹窗
    if (props.service) {
      const res = await props.service(formatData);
      ElMessage.success("操作成功");
      emit("success", res);
    }
    successDialog();
  } catch (error) {
    console.error(error);
  }
}

function reset() {
  ElFormRef.value?.resetFields();
}

// 清除特定字段的表单验证状态
function clearValidate(props?: string | string[]) {
  ElFormRef.value?.clearValidate(props);
}

defineExpose({
  clearValidate,
});
</script>

<template>
  <ElDialog
    :model-value="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="cancelDialog"
  >
    <slot name="header" :modelValue="formData" />
    <el-form
      v-if="items.length"
      ref="ElFormRef"
      v-bind="formAttrs"
      label-position="top"
      label-width="120px"
      :model="formData"
    >
      <div class="form-box">
        <div
          v-for="item in items"
          :key="item.formItem.prop"
          class="form-box-item"
          :style="item.width ? { width: item.width, padding: '10px' } : { padding: '10px' }"
        >
          <slot
            v-if="item.slotOnFormItem"
            :name="item.formItem.prop"
            :formItem="item"
            :props="item.props"
            :modelValue="formData[item.formItem.prop]"
            :set="(value: any) => assign(item.formItem.prop, value)"
          >
            <span>插槽覆盖整个表单项没有默认项</span>
          </slot>
          <el-form-item
            v-else-if="item.vIf ? item.vIf(formData) : true"
            class="w-full"
            v-bind="item.formItem"
            :required="item.formItem.required"
          >
            <slot
              :name="item.formItem.prop"
              :formData="formData"
              :props="item.props"
              :modelValue="formData[item.formItem.prop]"
              :set="(value: any) => assign(item.formItem.prop, value)"
            >
              <CustomComponent
                :is="item.is"
                v-model="formData[item.formItem.prop]"
                :props="item.props"
              />
            </slot>
          </el-form-item>
        </div>
        <div class="form-box-item footer">
          <el-form-item>
            <slot name="footer" :submit="submit" :reset="reset">
              <el-button type="primary" @click="submit">确定</el-button>
              <el-button @click="cancelDialog">取消</el-button>
            </slot>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <slot name="footer" :modelValue="formData" />
  </ElDialog>
</template>

<style scoped>
.el-form-item {
  margin-right: 0px !important;
  margin-bottom: 0px !important;
}
.form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.footer {
  margin-left: auto;
}
</style>
