<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { useRouter } from "vue-router";
import { opinionsPageApi, opinionsPageVO, opinionsExportApi } from "@/api/feedback/index";
import dayjs from "dayjs";
import { downloadBlobFile } from "@/utils/file";
import { ElMessageBox, ElMessage } from "element-plus";
import { ScoringCategoryDictMap } from "@/dicts/ScoringCategoryDicts";

const router = useRouter();

const entityCrudRef = ref<any>(null);

const config: EntityCrudProps<opinionsPageVO> = {
  entityName: "feedback",
  displayName: "意见征集汇总",
  hasIndexColumn: true,
  hasSelectionColumn: true,
  operations: [
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: async () => {
        const { getCurrentFormData, selectedRows } = entityCrudRef.value;
        const formData = getCurrentFormData();
        const idsList = selectedRows.map((row: any) => String(row.id)).join(",");
        const exportParams = {
          ...(idsList ? { ids: idsList } : {}),
          memberName: formData.memberName,
          startTime: formData.startTime,
          endTime: formData.endTime,
        };
        try {
          await ElMessageBox.confirm("确认要导出数据吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          const response = await opinionsExportApi(exportParams);
          if (response) {
            const {
              data,
              fileName = "意见征集汇总数据.xlsx",
              fileType = "application/vnd.ms-excel;charset=utf-8",
            } = response as any;
            const decodedFileName = decodeURIComponent(fileName);
            downloadBlobFile(data, decodedFileName, fileType);
          }
        } catch {
          ElMessage.info("已取消导出");
        }
      },
    },
  ],
  filterFormItems: {
    memberName: {
      type: "input",
      label: "商户会员名称",
    },
    createTime: {
      type: "date-range",
      label: "提交时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  tableColumns: {
    memberName: "商会会员名称",
    department: "所属单位",
    category: {
      label: "意见归属部门",
      formatter: (value: any) => {
        return ScoringCategoryDictMap.get(value)?.label || value || "";
      },
    },
    content: "意见描述",
    createTime: "提交时间",
    operations: {
      label: "操作",
    },
  },

  listFetchService: async (params) => {
    return opinionsPageApi(params);
  },
};
const getCheckDetail = (row: opinionsPageVO) => {
  router.push({
    path: "/feedback_mgmt/detail",
    query: {
      id: row.id,
    },
  });
};
const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #tableOperations="{ row }">
        <el-button type="primary" link @click="() => getCheckDetail(row)">查看</el-button>
      </template>
      <template #tableCreateTime="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format("YYYY-MM-DD HH:mm") : "-" }}
      </template>
    </EntityCrud>
  </div>
</template>

<style lang="scss" scoped></style>
