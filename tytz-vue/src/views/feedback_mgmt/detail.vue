<script setup lang="ts">
import { ref } from "vue";
import { getAssetsFileUrl } from "@/utils/file";
import { opinionsViewApi, opinionsPageVO } from "@/api/feedback/index";

import { useRoute } from "vue-router";
import { ScoringCategoryDictMap } from "@/dicts/ScoringCategoryDicts";

const route = useRoute();
const id = route.query.id as string;
const feedbackInfo = ref<opinionsPageVO>();
opinionsViewApi(id).then((res) => {
  feedbackInfo.value = res as opinionsPageVO;
});
</script>

<template>
  <div
    class="feedback-detail"
    :style="{
      'background-image': `url(${getAssetsFileUrl('page_bg.png')})`,
      'background-size': 'cover',
      'background-position': 'center',
    }"
  >
    <div class="detail-header">
      <span class="header-title">• 意见征集</span>
      <el-button type="primary" size="small" class="header-button" @click="$router.back()">
        返回
      </el-button>
    </div>

    <div class="detail-content">
      <div class="form-row">
        <div class="form-item">
          <span class="label">意见归属：</span>
          <span class="value">
            {{
              feedbackInfo?.category
                ? ScoringCategoryDictMap.get(feedbackInfo?.category)?.label ||
                  feedbackInfo?.category
                : ""
            }}
          </span>
        </div>
      </div>
      <div class="form-row">
        <div class="form-item">
          <span class="label">商会会员名称：</span>
          <span class="value">{{ feedbackInfo?.memberName }}</span>
        </div>
        <div class="form-item">
          <span class="label">所属单位：</span>
          <span class="value">{{ feedbackInfo?.department }}</span>
        </div>
      </div>
      <div class="form-row">
        <div class="form-item">
          <span class="label">联系方式：</span>
          <span class="value">{{ feedbackInfo?.contact }}</span>
        </div>
        <div class="form-item">
          <span class="label">提交时间：</span>
          <span class="value">{{ feedbackInfo?.createTime }}</span>
        </div>
      </div>
      <div class="form-row">
        <div class="form-item">
          <span class="label">意见描述：</span>
          <span class="value">{{ feedbackInfo?.content }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.feedback-detail {
  min-height: 100%;
  background-color: #fff;

  .detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    .header-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .detail-content {
    padding: 20px;

    .form-row {
      display: flex;
      justify-content: space-between;

      .form-item {
        flex: 0 0 calc(50% - 10px);
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 32px;
      }
    }

    .label {
      flex-shrink: 0;
      color: #606266;
      text-align: left;
    }
  }
}
</style>
