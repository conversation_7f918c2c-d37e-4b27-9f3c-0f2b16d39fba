<script setup lang="ts">
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EditableItem, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import displayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import type { DisplayableItem } from "@/components/EntityCrud/DisplayableDialog/type";
import {
  activityPageVO,
  activityPageApi,
  activityAddApi,
  activityUpdateApi,
  activityDeleteApi,
  activityViewApi,
} from "@/api/rating/activity";
//import UserAPI from "@/api/system/user";
// import DeptAPI from "@/api/system/dept";
import dayjs from "dayjs";
import { getChamberOfCommerceMembers } from "@/api/rating/member";
import { ChamberOfCommerceMemberVO } from "@/api/rating/types/member";
import {
  ScoringCategoryDictMap,
  // ScoringCategoryDictMap,
  ScoringCategoryDicts,
  ScoringCategoryEnum,
} from "@/dicts/ScoringCategoryDicts";
import { IActivityDetail } from "@/api/rating/types/activity";

/** 真正显示的活动详情数据类型 */
interface DetailVO extends Omit<IActivityDetail, "category" | "participants"> {
  /** 活动归属部门 - 转换为显示用的字符串格式 */
  category: string;
  /** 参与人员 - 转换为显示用的字符串格式 */
  participants: string;
  /** 活动时间 - 转换为时间范围格式 */
  activityTime: [string, string];
}

//PUBLIC_WELFARE: "参加商会组织的社会公益事业活动",
const typeEnum = {
  RESEARCH: "参加商会组织的调研、视察、考察等活动及商会组织的社会公益事业活动等",
  TRAINING: "参加市工商联和商会组织的培训活动",
  MEETING: "参加与总商会工作相关的各类会议与活动情况",
  SUPERVISION:
    "受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动",
  CONTRIBUTION: "以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献(以次数计)",
};

const entityCrudRef = ref<any>(null);

const dialogVisible = ref(false);
/** 当前要查看的活动详情 */
const detailData = ref<DetailVO | null>(null);
/** 详情表单项配置 */
const displayItems: DisplayableItem[] = [
  {
    label: "活动名称",
    prop: "title",
    type: "text",
  },
  {
    label: "活动类型",
    prop: "activityType",
    type: "text",
    formatter: (value: any) => {
      const key = value as keyof typeof typeEnum;
      return typeEnum[key];
    },
  },
  {
    label: "归属部门",
    prop: "category",
    type: "text",
    // NOTE: 无效
    formatter: (value: ScoringCategoryEnum[]) => {
      return value
        .map((item) => {
          return ScoringCategoryDictMap.get(item)?.label || item || "";
        })
        .join(",");
    },
  },
  {
    label: "参加人员",
    prop: "participants",
    type: "text",
    // NOTE: 无效
    formatter: (value: { id: string; businessName: string; businessMember?: string }[]) => {
      return value
        .map(
          (participant: any) => participant.businessName + "(" + participant.businessMember + ")"
        )
        .join("，");
    },
  },
  {
    label: "活动时间",
    prop: "activityTime",
    type: "date-range",
  },
  {
    label: "活动内容",
    prop: "content",
    type: "rich-text",
  },
  {
    label: "附件",
    prop: "attachments",
    type: "documents",
  },
];
const getActivityFormItems = (): EditableItem<activityPageVO> => {
  return {
    id: {
      type: "id",
    },
    title: {
      type: "input",
      label: "活动名称",
      required: true,
    },
    activityType: {
      type: "select",
      label: "活动类型",
      options: getEnumOptions(typeEnum),
      required: true,
    },
    category: {
      type: "select",
      label: "活动归属部门",
      options: ScoringCategoryDicts,
      required: true,
    },
    participants: {
      type: "select",
      label: "参加人员",
      required: true,
    },
    activityTime: {
      type: "date-picker",
      label: "活动时间",
      required: true,
    },
    content: {
      type: "editor",
      label: "活动内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "附件",
      maxFileCount: 10,
      tip: "(注：最多上传10个文件，可上传.pdf、.jpg、.png、.xlsx、.docx、.xls、.doc格式)",
    },
  };
};

const config: EntityCrudProps<activityPageVO> = {
  entityName: "activity",
  displayName: "活动信息",
  hasIndexColumn: true,
  filterFormItems: {
    title: {
      type: "input",
      label: "活动名称",
    },
    activityType: {
      type: "select",
      label: "活动类型",
      options: getEnumOptions(typeEnum),
    },
    activityTime: {
      type: "date-range",
      label: "活动时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  rowOperations: [
    {
      label: "详情",
      type: "link",
      displayIndex: -1,
      actionService: async (row) => {
        const result = await activityViewApi(row.id);
        detailData.value = {
          ...result,
          category: result.category
            ? result.category
                .map((item) => {
                  return ScoringCategoryDictMap.get(item)?.label || item || "";
                })
                .join("，")
            : "",
          activityType: typeEnum[result.activityType as keyof typeof typeEnum],
          activityTime: [result.startTime, result.endTime],
          participants: result.participants
            .map((participant) => participant.businessName + "(" + participant.businessMember + ")")
            .join("，"),
        };
        dialogVisible.value = true;
      },
    },
  ],
  createButtonLabel: "新增",
  tableColumns: {
    title: "活动名称",
    activityType: {
      label: "活动类型",
      formatter: (value: any) => {
        const key = value as keyof typeof typeEnum;
        return typeEnum[key];
      },
    },
    category: {
      label: "活动归属部门",
      formatter: (value: any) => {
        return value
          .map((item: ScoringCategoryEnum) => {
            return ScoringCategoryDictMap.get(item)?.label || item || "";
          })
          .join("，");
      },
    },
    startTime: {
      label: "活动开始时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    endTime: {
      label: "活动结束时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  },

  listFetchService: async (params) => {
    const result: any = await activityPageApi(params);
    result.list.forEach((item: any) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
      item.activityTime = [item.startTime, item.endTime];
      item.participants = item.participants
        ? JSON.parse(item.participants).map((participant: any) => participant.id.toString()) // 确保是字符串格式
        : [];
      item.category = item.category ? item.category.split(",") : [];
    });
    return Promise.resolve(result);
  },

  createFormItems: getActivityFormItems(),
  createService: (newRecord: activityPageVO) => {
    if (newRecord.activityTime && newRecord.activityTime.length >= 2) {
      const [startDate, endDate] = newRecord.activityTime;
      newRecord.startTime = `${startDate} 00:00:00`;
      newRecord.endTime = `${endDate} 23:59:59`;
    }
    if (Array.isArray(newRecord.participants)) {
      newRecord.participants = newRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete newRecord.activityTime;
    return activityAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: activityPageVO) => {
    if (updateRecord.activityTime && updateRecord.activityTime.length >= 2) {
      const [startDate, endDate] = updateRecord.activityTime;
      updateRecord.startTime = startDate.includes("T")
        ? startDate.replace("T", " ")
        : `${startDate} 00:00:00`;
      updateRecord.endTime = endDate.includes("T")
        ? endDate.replace("T", " ")
        : `${endDate} 23:59:59`;
    }
    if (Array.isArray(updateRecord.participants)) {
      updateRecord.participants = updateRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete updateRecord.activityTime;
    return activityUpdateApi(updateRecord.id, updateRecord);
  },
  deleteService: (row: activityPageVO) => {
    return activityDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);

/** 当前选中的会员所属部门列表 */
const selectedMemberDeptOptions = ref<string[]>([]);

/** 当前筛选关键字 */
const searchKeyword = ref("");

/** 总商会和新联会会员列表 */
const chamberOfCommerceMembers = ref<ChamberOfCommerceMemberVO[]>([]);

/** 获取总商会和新联会会员列表 */
const getMemberOptions = async () => {
  await getChamberOfCommerceMembers({}).then((res) => {
    if (Array.isArray(res)) {
      chamberOfCommerceMembers.value = res;
    } else {
      chamberOfCommerceMembers.value = [];
    }
  });
};

/** 参加成员下拉列表 */
const memberOptions = computed<
  { label: string; value: string; roleNames: string[]; roleCodes: string[] }[]
>(() => {
  return chamberOfCommerceMembers.value
    .filter(
      (item) =>
        (selectedMemberDeptOptions.value.length === 0 ||
          // TODO: 后期这里可能需要调整
          item.roles.some((role) => selectedMemberDeptOptions.value.includes(role.roleCode))) &&
        (searchKeyword.value === "" || item.nickname.includes(searchKeyword.value))
    )
    .map((item) => {
      return {
        label: `${item.nickname}（${item.company ? item.company : "-"}）- ${item.deptName}`,
        value: item.id,
        roleNames: item.roles.map((role) => role.roleName),
        roleCodes: item.roles.map((role) => role.roleCode),
      };
    });
});

const selectParticipants = ref<any>("");
const handleChangeSelect = (value: any) => {
  selectParticipants.value = value;
};

/** 选择活动归类事件处理 */
const handleChangeSelectCategory = (value: ScoringCategoryEnum[]) => {
  if (value && value.length > 0) {
    // const newArr: string[] = [];
    // value.forEach((item) => {
    //   newArr.push(ScoringCategoryDictMap.get(item)?.value || "");
    // });
    // selectedMemberDeptOptions.value = newArr.filter(Boolean);
    selectedMemberDeptOptions.value = value;
  } else {
    selectedMemberDeptOptions.value = [];
  }
};

getMemberOptions();
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #category="{ formData, props, change }">
        <el-select
          v-bind="props"
          v-model="formData.category"
          placeholder="请选择活动归类"
          multiple
          @change="handleChangeSelectCategory"
        >
          <el-option
            v-for="option in ScoringCategoryDicts"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
      <template #participants="{ formData, props, change }">
        <el-select
          v-model="formData.participants"
          placeholder="请选择参加人员"
          multiple
          @change="handleChangeSelect"
        >
          <template #header>
            <div class="flex-y-center">
              <div class="flex-y-center w-[300px]">
                <el-input v-model="searchKeyword" placeholder="输入人员名称查找" clearable />
              </div>
            </div>
          </template>

          <el-option
            v-for="option in memberOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </EntityCrud>
    <displayableDialog
      v-model:visible="dialogVisible"
      :display-data="detailData"
      :display-items="displayItems"
      title="详情"
      width="800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
