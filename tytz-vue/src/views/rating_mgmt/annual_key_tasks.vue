<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EditableItem, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import displayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import type { DisplayableItem } from "@/components/EntityCrud/DisplayableDialog/type";
import {
  annualKeyTasksPageVO,
  annualKeyTasksPageApi,
  annualKeyTasksAddApi,
  annualKeyTasksUpdateApi,
  annualKeyTasksDeleteApi,
  annualKeyTasksViewApi,
} from "@/api/rating/annualKeyTasks";
//import UserAPI from "@/api/system/user";
import dayjs from "dayjs";
// import DeptAPI from "@/api/system/dept";
import { getChamberOfCommerceMembers } from "@/api/rating/member";
import { ChamberOfCommerceMemberVO } from "@/api/rating/types/member";
import {
  ScoringCategoryDictMap,
  ScoringCategoryDicts,
  ScoringCategoryEnum,
} from "@/dicts/ScoringCategoryDicts";
import { IAnnualKeyTasksDetail } from "@/api/rating/types/annualKeyTasks";
import { AnnualKeyTasksTypeDictMap } from "./configs/dicts/AnnualKeyTasksTypeDict";

/** 重点工作详情数据类型 */
interface AnnualKeyTasksDetailVO extends Omit<IAnnualKeyTasksDetail, "category" | "participants"> {
  /** 工作归类 - 转换为显示用的字符串格式 */
  category: string;
  /** 参与人员 - 转换为显示用的字符串格式 */
  participants: string;
  /** 参加时间 - 转换为时间范围格式 */
  participationTime: [string, string];
}

const typeEnum = {
  PROJECT_SERVICE: "助力项目建设服务，参加引进外资活动",
  BUSINESS_ENVIRONMENT: "助推创一流营商环境",
  OTHER_TASKS: "完成区商会交办的其他任务",
};

const entityCrudRef = ref<any>(null);

const dialogVisible = ref(false);
/** 当前操作的重点工作详情 */
const detailData = ref<AnnualKeyTasksDetailVO | null>(null);
const displayItems: DisplayableItem[] = [
  {
    label: "年度工作",
    prop: "workName",
    type: "text",
  },
  {
    label: "工作类型",
    prop: "workType",
    type: "text",
    formatter: (value: string) => typeEnum[value as keyof typeof typeEnum],
  },
  {
    label: "归属部门",
    prop: "category",
    type: "text",
  },
  {
    label: "参加人员",
    prop: "participants",
    type: "text",
  },
  {
    label: "工作时间",
    prop: "participationTime",
    type: "date-range",
  },
  {
    label: "工作内容",
    prop: "workContent",
    type: "rich-text",
  },
  {
    label: "附件",
    prop: "attachments",
    type: "documents",
  },
];
const FormItems = (): EditableItem<annualKeyTasksPageVO> => {
  return {
    id: {
      type: "id",
    },
    year: {
      type: "input",
      label: "年度",
      required: true,
    },
    workName: {
      type: "input",
      label: "年度重点工作名称",
      required: true,
    },
    workType: {
      type: "select",
      label: "工作类型",
      options: getEnumOptions(typeEnum),
      required: true,
    },
    category: {
      type: "select",
      label: "工作归属部门",
      options: ScoringCategoryDicts,
      required: true,
    },
    participants: {
      type: "select",
      label: "参加人员",
      required: true,
    },
    participationTime: {
      type: "date-picker",
      label: "参加时间",
      required: true,
    },
    workContent: {
      type: "editor",
      label: "工作内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "附件",
      maxFileCount: 10,
      tip: "(注：最多上传10个文件，可上传.pdf、.jpg、.png、.xlsx、.docx、.xls、.doc格式)",
    },
  };
};

const config: EntityCrudProps<annualKeyTasksPageVO> = {
  entityName: "annualKeyTasks",
  displayName: "年度重点工作",
  hasIndexColumn: true,
  filterFormItems: {
    year: {
      type: "input",
      label: "年度",
    },
    workName: {
      type: "input",
      label: "工作名称",
    },
    participationTime: {
      type: "date-range",
      label: "参加时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  rowOperations: [
    {
      label: "详情",
      type: "link",
      displayIndex: -1,
      actionService: async (row) => {
        const result = await annualKeyTasksViewApi(row.id);
        detailData.value = {
          ...result,
          category: result.category
            ? result.category
                .map((item) => {
                  return ScoringCategoryDictMap.get(item)?.label || item || "";
                })
                .join("，")
            : "",
          workType: AnnualKeyTasksTypeDictMap.get(result.workType)?.label || result.workType || "",
          participationTime: [result.startTime, result.endTime],
          participants: result.participants
            .map(
              (participant: any) =>
                participant.businessName + "(" + participant.businessMember + ")"
            )
            .join("，"),
        };
        dialogVisible.value = true;
      },
    },
  ],
  createButtonLabel: "新增",
  tableColumns: {
    year: "年度",
    workName: "工作名称",
    workType: {
      label: "工作类型",
      formatter: (value: any) => {
        return AnnualKeyTasksTypeDictMap.get(value)?.label || value || "";
      },
    },
    category: {
      label: "工作归属部门",
      formatter: (value: any) => {
        return value && Array.isArray(value) && value.length > 0
          ? value
              .map((item: ScoringCategoryEnum) => {
                return ScoringCategoryDictMap.get(item)?.label || item || "";
              })
              .join("，")
          : "--";
      },
    },
    startTime: {
      label: "开始时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    endTime: {
      label: "结束时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  },

  listFetchService: async (params) => {
    const result: any = await annualKeyTasksPageApi(params);
    result.list.forEach((item: any) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
      item.participationTime = [item.startTime, item.endTime];
      item.participants = item.participants
        ? JSON.parse(item.participants).map((participant: any) => participant.id.toString()) // 确保是字符串格式
        : [];
      item.category = item.category ? item.category.split(",") : [];
    });
    return Promise.resolve(result);
  },
  createFormItems: FormItems(),
  createService: (newRecord: annualKeyTasksPageVO) => {
    if (newRecord.participationTime && newRecord.participationTime.length >= 2) {
      const [startDate, endDate] = newRecord.participationTime;
      newRecord.startTime = `${startDate} 00:00:00`;
      newRecord.endTime = `${endDate} 23:59:59`;
    }
    if (Array.isArray(newRecord.participants)) {
      newRecord.participants = newRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete newRecord.participationTime;
    return annualKeyTasksAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: annualKeyTasksPageVO) => {
    if (updateRecord.participationTime && updateRecord.participationTime.length >= 2) {
      const [startDate, endDate] = updateRecord.participationTime;
      updateRecord.startTime = startDate.includes("T")
        ? startDate.replace("T", " ")
        : `${startDate} 00:00:00`;
      updateRecord.endTime = endDate.includes("T")
        ? endDate.replace("T", " ")
        : `${endDate} 23:59:59`;
    }
    if (Array.isArray(updateRecord.participants)) {
      updateRecord.participants = updateRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete updateRecord.participationTime;
    return annualKeyTasksUpdateApi(updateRecord.id, updateRecord);
  },
  deleteService: (row: annualKeyTasksPageVO) => {
    return annualKeyTasksDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);

/** 当前选中的会员所属部门列表 */
const selectedMemberDeptOptions = ref<string[]>([]);

/** 当前筛选关键字 */
const searchKeyword = ref("");

/** 总商会和新联会会员列表 */
const chamberOfCommerceMembers = ref<ChamberOfCommerceMemberVO[]>([]);

/** 获取总商会和新联会会员列表 */
const getMemberOptions = async () => {
  await getChamberOfCommerceMembers({}).then((res) => {
    if (Array.isArray(res)) {
      chamberOfCommerceMembers.value = res;
    } else {
      chamberOfCommerceMembers.value = [];
    }
  });
};

/** 参加成员下拉列表 */
const memberOptions = computed<
  { label: string; value: string; roleNames: string[]; roleCodes: string[] }[]
>(() => {
  return chamberOfCommerceMembers.value
    .filter(
      (item) =>
        (selectedMemberDeptOptions.value.length === 0 ||
          item.roles.some((role) => selectedMemberDeptOptions.value.includes(role.roleCode))) &&
        (searchKeyword.value === "" || item.nickname.includes(searchKeyword.value))
    )
    .map((item) => {
      return {
        label: `${item.nickname}（${item.company ? item.company : "-"}）- ${item.deptName}`,
        value: item.id,
        roleNames: item.roles.map((role) => role.roleName),
        roleCodes: item.roles.map((role) => role.roleCode),
      };
    });
});

const selectParticipants = ref<any>("");
const handleChangeSelect = (value: any) => {
  selectParticipants.value = value;
};

/** 选择活动归类事件处理 */
const handleChangeSelectCategory = (value: ScoringCategoryEnum[]) => {
  if (value && value.length > 0) {
    selectedMemberDeptOptions.value = value;
  } else {
    selectedMemberDeptOptions.value = [];
  }
};
getMemberOptions();
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #category="{ formData, props, change }">
        <el-select
          v-bind="props"
          v-model="formData.category"
          multiple
          @change="handleChangeSelectCategory"
        >
          <el-option
            v-for="option in ScoringCategoryDicts"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>

      <template #participants="{ formData, props, change }">
        <el-select
          v-model="formData.participants"
          placeholder="请选择参加人员"
          multiple
          @change="handleChangeSelect"
        >
          <template #header>
            <div class="flex-y-center">
              <div class="flex-y-center">
                <el-input v-model="searchKeyword" placeholder="输入人员名称查找" clearable />
              </div>
            </div>
          </template>

          <el-option
            v-for="option in memberOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </EntityCrud>
    <displayableDialog
      v-model:visible="dialogVisible"
      :display-data="detailData"
      :display-items="displayItems"
      title="详情"
      width="800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
