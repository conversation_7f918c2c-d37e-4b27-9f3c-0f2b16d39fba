import { dictsToMap } from "@/utils/dicts";

/**
 * 会议类型枚举
 */
export enum MeetingTypeEnum {
  /** 参加会员(代表)大会 */
  MEMBER = "MEMBER",
  /** 参加会长会议 */
  PRESIDENT = "PRESIDENT",
  /** 参加理事会会议 */
  DIRECTOR = "DIRECTOR",
}

/**
 * 会议类型字典数据
 */
export const MeetingTypeDicts: Dict<MeetingTypeEnum>[] = [
  { value: MeetingTypeEnum.MEMBER, label: "参加会员(代表)大会", sort: 1 },
  { value: MeetingTypeEnum.PRESIDENT, label: "参加会长会议", sort: 2 },
  { value: MeetingTypeEnum.DIRECTOR, label: "参加理事会会议", sort: 3 },
];

/**
 * 会议类型字典数据Map
 */
export const MeetingTypeDictMap = dictsToMap(MeetingTypeDicts);
