import { dictsToMap } from "@/utils/dicts";

/**
 * 年度重点工作类型枚举
 */
export enum AnnualKeyTasksTypeEnum {
  /** 助力项目建设服务，参加引进外资活动 */
  PROJECT_SERVICE = "PROJECT_SERVICE",
  /** 助推创一流营商环境 */
  BUSINESS_ENVIRONMENT = "BUSINESS_ENVIRONMENT",
  /** 完成区商会交办的其他任务 */
  OTHER_TASKS = "OTHER_TASKS",
}

/**
 * 年度重点工作类型字典数据
 */
export const AnnualKeyTasksTypeDicts: Dict<AnnualKeyTasksTypeEnum>[] = [
  {
    value: AnnualKeyTasksTypeEnum.PROJECT_SERVICE,
    label: "助力项目建设服务，参加引进外资活动",
    sort: 1,
  },
  { value: AnnualKeyTasksTypeEnum.BUSINESS_ENVIRONMENT, label: "助推创一流营商环境", sort: 2 },
  { value: AnnualKeyTasksTypeEnum.OTHER_TASKS, label: "完成区商会交办的其他任务", sort: 3 },
];

/**
 * 年度重点工作类型字典数据Map
 */
export const AnnualKeyTasksTypeDictMap = dictsToMap(AnnualKeyTasksTypeDicts);
