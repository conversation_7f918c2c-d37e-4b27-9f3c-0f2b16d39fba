import { dictsToMap } from "@/utils/dicts";

/**
 * 活动类型枚举
 */
export enum ActivityTypeEnum {
  /** 参加商会组织的调研、视察、考察等活动及商会组织的社会公益事业活动等 */
  RESEARCH = "RESEARCH",
  /** 参加市工商联和商会组织的培训活动 */
  TRAINING = "TRAINING",
  /** 参加与总商会工作相关的各类会议与活动情况 */
  MEETING = "MEETING",
  /** 受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动 */
  SUPERVISION = "SUPERVISION",
  /** 以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献(以次数计) */
  CONTRIBUTION = "CONTRIBUTION",
}

/**
 * 活动类型字典数据
 */
export const ActivityTypeDicts: Dict<ActivityTypeEnum>[] = [
  {
    value: ActivityTypeEnum.RESEARCH,
    label: "参加商会组织的调研、视察、考察等活动及商会组织的社会公益事业活动等",
    sort: 1,
  },
  { value: ActivityTypeEnum.TRAINING, label: "参加市工商联和商会组织的培训活动", sort: 2 },
  { value: ActivityTypeEnum.MEETING, label: "参加与总商会工作相关的各类会议与活动情况", sort: 3 },
  {
    value: ActivityTypeEnum.SUPERVISION,
    label: "受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动",
    sort: 4,
  },
  {
    value: ActivityTypeEnum.CONTRIBUTION,
    label: "以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献",
    sort: 5,
  },
];

/**
 * 活动类型字典数据Map
 */
export const ActivityTypeDictMap = dictsToMap(ActivityTypeDicts);
