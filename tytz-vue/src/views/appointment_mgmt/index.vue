<template>
  <div>
    <div class="entity-crud-wrapper">
      <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
        <template #tableAppointmentStartTime="{ row }">
          <div>{{ dayjs(row.appointmentStartTime).format("YYYY-MM-DD HH:mm") }}</div>
        </template>
        <template #tableAppointmentEndTime="{ row }">
          <div>{{ dayjs(row.appointmentEndTime).format("YYYY-MM-DD HH:mm") }}</div>
        </template>
        <template #tableAppointmentStatus="{ row }: { row: appointmentsPageVO }">
          <div
            class="flex"
            :style="{
              color: statusStyleEnum[row.appointmentStatus as keyof typeof statusEnum]?.color,
            }"
          >
            <div
              class="pointer"
              :style="{
                backgroundColor:
                  statusStyleEnum[row.appointmentStatus as keyof typeof statusEnum]?.color,
              }"
            ></div>
            {{ statusEnum[row.appointmentStatus as keyof typeof statusEnum] }}
          </div>
        </template>
      </EntityCrud>
    </div>
    <EditableDialog v-bind="updateFormProps" ref="updateFormRef"></EditableDialog>
    <AppointmentDetail
      v-model:visible="showDetailDialog"
      :data="currentAppointment"
      @confirm="handleDetailConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, useTemplateRef } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudInstance, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import AppointmentDetail from "./components/AppointmentDetail.vue";
import { useEditableDialog } from "@/components/EntityCrud/EditableDialog/hook";
import EditableDialog from "@/components/EditableForm/index.vue";
import { statusEnum, statusStyleEnum } from "./enum";
import { downloadBlobFile } from "@/utils/file";
import {
  appointmentsPageVO,
  appointmentPageApi,
  handleAppointmentApi,
  feedbackApi,
  feedbackExportApi,
  appointmentBatchDeleteApi,
} from "@/api/appointment/index";
import dayjs from "dayjs";
import { ElMessageBox, ElMessage } from "element-plus";
import { useUserStore } from "@/store/modules/user";
import { ActionTypeEnum } from "@/enums/ActionTypeEnum";

/** 是否正在提交 */
const isSubmitting = ref(false);

/** 当前操作类型 */
const currentActionType = ref<ActionTypeEnum>(ActionTypeEnum.VIEW);

const userStore = useUserStore();
/** EntityCrud组件的引用ref */
const entityCrudRef = useTemplateRef<EntityCrudInstance<appointmentsPageVO>>("entityCrudRef");

const updateFormRef = ref<any>(null);
const showFeedbackDialog = async (data: appointmentsPageVO) => {
  data.feedback = "";
  await showUpdateDialog(data);
};
const { formProps: updateFormProps, showDialog: showUpdateDialog } = useEditableDialog({
  title: "约见反馈",
  formItems: {
    id: {
      type: "id",
      label: "id",
    },
    feedback: {
      type: "editor",
      label: "",
    },
  },
  submitRequest: async (data: any) => {
    const id = data.id;
    delete data.id;
    return feedbackApi(id, data)
      .then(() => {
        entityCrudRef.value?.handleRefresh();
      })
      .catch((err) => {
        console.log(err);
      });
  },

  width: "800px",
});

const config: EntityCrudProps<appointmentsPageVO> = {
  entityName: "appo_transact",
  displayName: "约见办理",
  hasIndexColumn: true,
  hasSelectionColumn: true,
  operations: [
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: async () => {
        const formData: any = entityCrudRef.value?.getCurrentFormData();
        const selectedRows = entityCrudRef.value?.getSelectedRows() || [];
        const idsList = selectedRows.map((row) => String(row.id)).join(",");
        const exportParams = {
          ...(idsList ? { ids: idsList } : {}),
          appointmentName: formData.appointmentName,
          appointmentStatus: formData.appointmentStatus,
          startTimeBegin: formData.startTimeBegin,
          startTimeEnd: formData.startTimeEnd,
        };

        try {
          await ElMessageBox.confirm("确认要导出数据吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          const response = await feedbackExportApi(exportParams);
          if (response) {
            const {
              data,
              fileName = "约见办理数据.xlsx",
              fileType = "application/vnd.ms-excel;charset=utf-8",
            } = response as any;
            const decodedFileName = decodeURIComponent(fileName);
            downloadBlobFile(data, decodedFileName, fileType);
          }
        } catch {
          ElMessage.info("已取消导出");
        }
      },
    },
    {
      type: "button",
      label: "删除",
      colorize: "danger",
      actionService: async () => {
        const selectedRows = entityCrudRef.value?.getSelectedRows() || [];
        await handleDelete(selectedRows, true);
      },
    },
  ],
  filterFormItems: {
    appointmentName: {
      type: "input",
      label: "约见人名称",
    },
    appointmentStatus: {
      type: "select",
      label: "约见状态",
      options: getEnumOptions(statusEnum),
    },
    createTime: {
      type: "date-range",
      label: "约见时间",
      dateRangeFields: ["startTimeBegin", "startTimeEnd"],
    },
  },
  tableColumns: {
    appointmentName: "约见人名称",
    appointmentUnit: "所属单位",
    appointmentDepartment: "被约见部委",
    appointmentStartTime: "约见开始时间",
    appointmentEndTime: "约见结束时间",
    appointmentStatus: "约见状态",
    operations: {
      label: "操作",
      // width: 200,
    },
  },
  rowOperations: [
    {
      label: "约见",
      type: "link",
      displayIndex: -1,
      canDisplay: (row) => {
        return row.appointmentStatus === statusStyleEnum.WAIT.value;
      },
      actionService: async (row: appointmentsPageVO) => {
        currentActionType.value = ActionTypeEnum.APPOINTMENT;
        currentAppointment.value = row;
        showDetailDialog.value = true;
      },
    },
    {
      label: "查看",
      type: "link",
      displayIndex: 1,
      canDisplay: (row) => {
        // return row.handleStatus !== statusStyleEnum.WAIT.value;
        return row.appointmentStatus !== statusStyleEnum.WAIT.value;
      },
      actionService: async (row: appointmentsPageVO) => {
        currentActionType.value = ActionTypeEnum.VIEW;
        currentAppointment.value = row;
        showDetailDialog.value = true;
      },
    },
    {
      label: "反馈",
      type: "link",
      displayIndex: 2,
      canDisplay: (row) => {
        return row.appointmentStatus == statusStyleEnum.END.value && !row.feedback;
      },
      actionService: async (row) => {
        currentActionType.value = ActionTypeEnum.FEEDBACK;
        await showFeedbackDialog(row);
      },
    },
    {
      label: "删除",
      type: "link",
      displayIndex: 2,
      colorize: () => "danger",
      canDisplay: (row) => {
        return row.appointmentStatus == statusStyleEnum.WAIT.value;
      },
      actionService: async (rowData: appointmentsPageVO) => {
        currentActionType.value = ActionTypeEnum.DELETE;
        await handleDelete([rowData], false);
      },
    },
  ],

  listFetchService: async (params) => {
    return appointmentPageApi(params);
  },
};

/** EntityCrud 组件的 pops */
const entityCrudProps = defineEntityCrud(config);

const showDetailDialog = ref(false);
const currentAppointment = ref<appointmentsPageVO>();

/** 详情/约见弹窗确认 */
const handleDetailConfirm = async (formData: any) => {
  // 查看的话直接关闭弹框就行
  if (currentActionType.value === ActionTypeEnum.VIEW) {
    showDetailDialog.value = false;
    return;
  }

  try {
    const id = formData.id;
    delete formData.id;

    await handleAppointmentApi(id, formData);
    showDetailDialog.value = false;
    entityCrudRef.value?.handleRefresh();
    userStore.fetchAppointmentCount();
  } catch (err: any) {
    if (err.msg) ElMessage.error(err.msg);
  } finally {
    entityCrudRef.value?.handleRefresh();
  }
};

/** 删除操作 */
const handleDelete = async (rows: appointmentsPageVO[], isBatch = false) => {
  try {
    if (isSubmitting.value) {
      return;
    }
    if (isBatch) {
      if (rows.length === 0) {
        ElMessage.warning("请先选择要删除的数据项");
        return;
      }
    }
    // 接口调用时的参数
    const params = {
      ids: rows.map((row) => row.id),
    };

    await ElMessageBox.confirm("数据删除后将不可恢复！是否确认删除数据？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    isSubmitting.value = true;
    await appointmentBatchDeleteApi(params).finally(() => {
      isSubmitting.value = false;
      // 刷新表格
      entityCrudRef.value?.handleRefresh();
    });
  } catch {
    console.log("取消删除");
  }
};
</script>

<style lang="scss" scoped>
.pointer {
  width: 8px;
  height: 8px;
  margin-top: 7px;
  margin-right: 4px;
  border-radius: 50%;
}
</style>
./enum
