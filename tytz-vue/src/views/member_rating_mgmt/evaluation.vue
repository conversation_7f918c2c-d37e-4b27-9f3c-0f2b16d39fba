<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { defineSearchPage } from "@/components/SearchPage/hook";
import SearchPage from "@/components/SearchPage/index.vue";
import { downloadBlobFile } from "@/utils/file";
import {
  scoringRecordPageApi,
  scoringRecordPageVO,
  scoringRecordDetailApi,
  scoringRecordDetailExportApi,
} from "@/api/scoring_records/index";
import { ElMessageBox, ElMessage } from "element-plus";
import { ScoringCategoryDicts } from "@/dicts/ScoringCategoryDicts";

// 常量定义
const SCORING_TYPE = {
  ACTIVITY: {
    label: "参加活动",
    value: "ACTIVITY",
    tableColumns: {
      index: { label: "序号", type: "index" },
      activityName: "参加活动",
      activityType: "活动类型",
      score: "获得评分",
    },
  },
  MEETING: {
    label: "参加会议",
    value: "MEETING",
    tableColumns: {
      index: { label: "序号", type: "index" },
      meetingName: "参加会议",
      meetingType: "会议类型",
      score: "获得评分",
    },
  },
  KEY_WORK: {
    label: "重点工作",
    value: "KEY_WORK",
    tableColumns: {
      index: { label: "序号", type: "index" },
      workName: "年度重点工作情况",
      workType: "年度重点工作类型",
      score: "获得评分",
    },
  },
  ENVIRONMENT: {
    label: "营商环境问题报送",
    value: "ENVIRONMENT",
    tableColumns: {
      index: { label: "序号", type: "index" },
      problemTitle: "营商环境问题报送",
      scoringDetail: "营商环境类别",
      score: "获得评分",
    },
  },
  OPINION: {
    label: "意见建议",
    value: "OPINION",
    tableColumns: {
      index: { label: "序号", type: "index" },
      opinionContent: "意见内容",
      score: "获得评分",
    },
  },
} as const;

/** 会员所属部门下列表 */
const memberDeptOptions = ref(ScoringCategoryDicts);

// 类型定义
type ScoringType = keyof typeof SCORING_TYPE;
type DetailDialogRef = {
  visible: boolean;
  type: ScoringType;
};

// 组件引用
const entityCrudRef = ref<any>(null);
const detailData = ref<any>();
const dialogTotalScore = ref(0);

// 详情弹窗状态管理
const detailDialogs = ref<Record<ScoringType, DetailDialogRef>>({
  ACTIVITY: { visible: false, type: "ACTIVITY" },
  MEETING: { visible: false, type: "MEETING" },
  KEY_WORK: { visible: false, type: "KEY_WORK" },
  ENVIRONMENT: { visible: false, type: "ENVIRONMENT" },
  OPINION: { visible: false, type: "OPINION" },
});

// 创建详情列表配置
const createDetailListProps = (type: ScoringType) => {
  return defineSearchPage<any, any>({
    tableColumns: SCORING_TYPE[type].tableColumns,
    service: async (params) => {
      const result: any = await scoringRecordDetailApi({
        ...params,
        memberId: detailData.value.memberId,
        year: detailData.value.year,
        scoringType: SCORING_TYPE[type].value,
        category: detailData.value.category,
      });
      dialogTotalScore.value = result.list.reduce(
        (total: number, item: any) => total + parseFloat(item.score),
        0
      );
      return result;
    },
  });
};

// 详情列表配置
const detailListProps = {
  ACTIVITY: createDetailListProps("ACTIVITY"),
  MEETING: createDetailListProps("MEETING"),
  KEY_WORK: createDetailListProps("KEY_WORK"),
  ENVIRONMENT: createDetailListProps("ENVIRONMENT"),
  OPINION: createDetailListProps("OPINION"),
};

// 处理查看详情
const handleCheckDetail = (row: scoringRecordPageVO, type: ScoringType) => {
  detailData.value = row;
  detailDialogs.value[type].visible = true;
};

// 主列表配置
const config: EntityCrudProps<scoringRecordPageVO> = {
  entityName: "evaluation",
  displayName: "评价表管理",
  hasIndexColumn: true,
  hasSelectionColumn: true,
  operations: [
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: async () => {
        const { getCurrentFormData, selectedRows } = entityCrudRef.value;
        const formData = getCurrentFormData();
        const idsList = selectedRows.map((row: any) => String(row.id)).join(",");
        const exportParams = {
          ...(idsList ? { ids: idsList } : {}),
          year: formData.year,
          memberName: formData.memberName,
          department: formData.department,
        };
        try {
          await ElMessageBox.confirm("确认要导出数据吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          const response = await scoringRecordDetailExportApi(exportParams);
          if (response) {
            const {
              data,
              fileName = "评价表管理数据.xlsx",
              fileType = "application/vnd.ms-excel;charset=utf-8",
            } = response as any;
            downloadBlobFile(data, decodeURIComponent(fileName), fileType);
          }
        } catch {
          ElMessage.info("已取消导出");
        }
      },
    },
  ],
  filterFormItems: {
    year: {
      type: "date",
      label: "年度",
    },
    category: {
      type: "select",
      label: "会员所属商会",
      options: memberDeptOptions.value,
    },
    memberName: {
      type: "input",
      label: "会员名称",
    },
    department: {
      type: "input",
      label: "所属单位",
    },
  },
  tableColumns: {
    year: "年度",
    memberName: "会员名称",
    // memberDeptName: "会员所属商会",
    categoryLabel: "会员所属商会",
    department: "所属单位",
    activeCount: "参加活动(次)",
    meetingCount: "参加会议（次）",
    keyWorkCount: "年度重点工作(次)",
    environmentCount: "营商环境问题报送(次)",
    opinionCount: "意见建议(次)",
    totalScore: "总分",
  },
  listFetchService: async (params) => {
    if (!params.year) {
      params.year = new Date().getFullYear();
    }
    return scoringRecordPageApi(params);
  },
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div class="tea-origin-container">
    <div class="entity-crud-wrapper">
      <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
        <template #tableActiveCount="{ row }">
          <el-button type="primary" link @click="handleCheckDetail(row, 'ACTIVITY')">
            {{ row.activeCount || 0 }}
          </el-button>
        </template>
        <template #tableMeetingCount="{ row }">
          <el-button type="primary" link @click="handleCheckDetail(row, 'MEETING')">
            {{ row.meetingCount || 0 }}
          </el-button>
        </template>
        <template #tableKeyWorkCount="{ row }">
          <el-button type="primary" link @click="handleCheckDetail(row, 'KEY_WORK')">
            {{ row.keyWorkCount || 0 }}
          </el-button>
        </template>
        <template #tableEnvironmentCount="{ row }">
          <el-button type="primary" link @click="handleCheckDetail(row, 'ENVIRONMENT')">
            {{ row.environmentCount || 0 }}
          </el-button>
        </template>
        <template #tableOpinionCount="{ row }">
          <el-button type="primary" link @click="handleCheckDetail(row, 'OPINION')">
            {{ row.opinionCount || 0 }}
          </el-button>
        </template>
      </EntityCrud>
    </div>

    <!-- 详情弹窗 -->
    <template v-for="(dialog, type) in detailDialogs" :key="type">
      <el-dialog
        v-model="dialog.visible"
        :modal="true"
        :close-on-click-modal="true"
        :show-close="false"
        :append-to-body="true"
        title=""
        center
      >
        <div class="text-sm font-bold mb-5">总得分： {{ dialogTotalScore }}</div>
        <SearchPage v-bind="detailListProps[type]" />
      </el-dialog>
    </template>
  </div>
</template>

<style scoped>
.tea-origin-container {
  width: 100%;
}

.entity-crud-wrapper {
  width: 100%;
}
</style>
