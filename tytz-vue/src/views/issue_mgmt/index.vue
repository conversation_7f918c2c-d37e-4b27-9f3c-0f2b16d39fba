<script setup lang="ts">
import { ref } from "vue";
import { problemsPageApi, ProblemsPageVO, problemsExportApi } from "@/api/biz_env/issue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import { useRouter } from "vue-router";
import { downloadBlobFile } from "@/utils/file";
import dayjs from "dayjs";
import { ElMessageBox, ElMessage } from "element-plus";
import { ScoringCategoryDictMap } from "@/dicts/ScoringCategoryDicts";

const router = useRouter();

const statusEnum = {
  WAIT: "待处理",
  PASS: "已采纳",
  REJECT: "未采纳",
};

const entityCrudRef = ref<any>(null);

const config: EntityCrudProps<ProblemsPageVO> = {
  entityName: "issue",
  displayName: "问题审核",
  hasIndexColumn: true,
  hasSelectionColumn: true,
  operations: [
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: async () => {
        const { getCurrentFormData, selectedRows } = entityCrudRef.value;
        const formData = getCurrentFormData();
        const idsList = selectedRows.map((row: any) => String(row.id)).join(",");
        const exportParams = {
          ...(idsList ? { ids: idsList } : {}),
          adoptStatus: formData.adoptStatus,
          startTime: formData.startTime,
          endTime: formData.endTime,
        };
        try {
          await ElMessageBox.confirm("确认要导出数据吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          const response = await problemsExportApi(exportParams);
          if (response) {
            const {
              data,
              fileName = "营商环境问题数据.xlsx",
              fileType = "application/vnd.ms-excel;charset=utf-8",
            } = response as any;
            const decodedFileName = decodeURIComponent(fileName);
            downloadBlobFile(data, decodedFileName, fileType);
          }
        } catch {
          ElMessage.info("已取消导出");
        }
      },
    },
  ],
  filterFormItems: {
    memberName: {
      type: "input",
      label: "商户会员名称",
    },
    adoptStatus: {
      type: "select",
      label: "采纳状态",
      options: getEnumOptions(statusEnum),
    },
    createTime: {
      type: "date-range",
      label: "提交时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  tableColumns: {
    memberName: "商会会员名称",
    department: "所属单位",
    businessType: "营商环境类别",
    category: {
      label: "问题归属部门",
      formatter: (value: any) => {
        return ScoringCategoryDictMap.get(value)?.label || value || "";
      },
    },
    title: "标题",
    createTime: "提交时间",
    adoptStatus: "处理状态",
    operations: {
      label: "操作",
    },
  },
  listFetchService: async (params) => {
    return problemsPageApi(params);
  },
};
const handleCheckDetail = (row: ProblemsPageVO) => {
  router.push({
    path: "/issue_mgmt/detail",
    query: {
      id: row.id,
      title: "审核详情",
    },
  });
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #tableAdoptStatus="{ row }: { row: ProblemsPageVO }">
        <el-tag
          :type="
            row.adoptStatus === 'WAIT'
              ? 'warning'
              : row.adoptStatus === 'REJECT'
                ? 'danger'
                : 'success'
          "
        >
          {{ row.adoptStatus ? statusEnum[row.adoptStatus as keyof typeof statusEnum] : "-" }}
        </el-tag>
      </template>
      <template #tableCreateTime="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format("YYYY-MM-DD HH:mm") : "-" }}
      </template>
      <template #tableOperations="{ row }">
        <el-button type="primary" link @click="handleCheckDetail(row)">
          {{ row.adoptStatus === "WAIT" ? "审核" : "查看" }}
        </el-button>
      </template>
    </EntityCrud>
  </div>
</template>

<style lang="scss" scoped></style>
