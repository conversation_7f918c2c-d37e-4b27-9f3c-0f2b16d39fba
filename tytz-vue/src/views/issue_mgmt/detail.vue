<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getAssetsFileUrl } from "@/utils/file";
import {
  ProblemsPageVO,
  problemsAdoptApi,
  problemsViewApi,
  adoptProblemsForm,
} from "@/api/biz_env/issue";
import dayjs from "dayjs";
import { ScoringCategoryDictMap } from "@/dicts/ScoringCategoryDicts";

const route = useRoute();
const router = useRouter();
const id = route.query.id as string;

// 定义枚举类型
enum AdoptStatus {
  WAIT = "WAIT",
  PASS = "PASS",
  REJECT = "REJECT",
}

const AdoptStatusEnum = {
  [AdoptStatus.WAIT]: { label: "待处理", value: AdoptStatus.WAIT, color: "#409EFF" },
  [AdoptStatus.PASS]: { label: "予以采纳", value: AdoptStatus.PASS, color: "#67C23A" },
  [AdoptStatus.REJECT]: { label: "不予采纳", value: AdoptStatus.REJECT, color: "#F56C6C" },
} as const;

const detail = ref<ProblemsPageVO>({
  memberName: "",
  department: "",
  businessType: "",
  submitTime: "",
  title: "",
  content: "",
  adoptStatus: AdoptStatusEnum.PASS.value,
});

const form = ref<adoptProblemsForm>({
  id: id,
  adoptStatus: AdoptStatusEnum.PASS.value,
  adoptContent: "",
});

// 是否已处理
const isHandled = computed(() => {
  return detail.value.adoptStatus == AdoptStatusEnum.WAIT.value;
});

const isLoading = ref(false);
// 提交处理
const handleSubmit = () => {
  isLoading.value = true;
  // TODO: 调用接口提交处理结果
  if (form.value.adoptStatus === AdoptStatusEnum.REJECT.value && !form.value.adoptContent) {
    ElMessage.warning("请输入审核意见");
    return;
  }
  problemsAdoptApi(form.value)
    .then(() => {
      ElMessage.success("提交成功");
      isLoading.value = false;
      router.back();
    })
    .catch((err) => {
      console.log(err);
      ElMessage.error("提交失败");
    })
    .finally(() => {
      isLoading.value = false;
    });
};

problemsViewApi(id).then((res) => {
  detail.value = res as ProblemsPageVO;
});
</script>

<template>
  <div
    class="issue-detail"
    :style="{
      'background-image': `url(${getAssetsFileUrl('page_bg.png')})`,
      'background-size': 'cover',
      'background-position': 'center',
    }"
  >
    <!-- 处理内容 -->
    <div class="section">
      <div class="section-title">
        <div class="title-left">
          <i class="dot"></i>
          <span>内容详情</span>
        </div>
        <el-button class="return-btn" type="primary" @click="$router.back()">返回</el-button>
      </div>
      <div class="content-form">
        <div class="form-row">
          <div class="form-item">
            <span class="label">商会会员名称：</span>
            <span class="value">{{ detail.memberName }}</span>
          </div>
          <div class="form-item">
            <span class="label">所属单位：</span>
            <span class="value">{{ detail.department || "-" }}</span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <span class="label">营商环境问题归属：</span>
            <span class="value">
              {{ detail?.category ? ScoringCategoryDictMap.get(detail?.category)?.label : "" }}
            </span>
          </div>
          <div class="form-item">
            <span class="label">营商环境类别：</span>
            <span class="value">{{ detail.businessType }}</span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <span class="label">营商环境标题：</span>
            <span class="value">{{ detail.title }}</span>
          </div>
          <div class="form-item">
            <span class="label">提交时间：</span>
            <span class="value">{{ dayjs(detail.submitTime).format("YYYY-MM-DD HH:mm") }}</span>
          </div>
        </div>
        <div class="form-item description-item">
          <span class="label">营商环境问题描述：</span>
          <div class="value">{{ detail.content }}</div>
        </div>
      </div>
    </div>

    <!-- 处理评情 -->
    <div class="section">
      <div class="section-title">
        <div class="title-left">
          <i class="dot"></i>
          <span>采纳详情</span>
        </div>
      </div>
      <div v-if="isHandled" class="handle-form">
        <el-form ref="formRef" :model="form">
          <el-form-item label="审核结果" required>
            <el-radio-group v-model="form.adoptStatus">
              <el-radio :value="AdoptStatusEnum.PASS.value">
                {{ AdoptStatusEnum.PASS.label }}
              </el-radio>
              <el-radio :value="AdoptStatusEnum.REJECT.value">
                {{ AdoptStatusEnum.REJECT.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="审核意见"
            prop="adoptContent"
            show-word-limit
            :rules="[
              {
                required: form.adoptStatus == AdoptStatusEnum.REJECT.value,
                message: '请输入审核意见',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              v-model="form.adoptContent"
              type="textarea"
              :rows="10"
              :maxlength="200"
              show-word-limit
              placeholder="请输入审核意见....."
            />
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </div>
      <div v-else class="handle-result">
        <div class="flex">
          <div class="result-item">
            <span class="label">采纳结果：</span>
            <div
              class="value flex"
              :style="{
                color: AdoptStatusEnum[detail.adoptStatus as keyof typeof AdoptStatusEnum].color,
              }"
            >
              <div
                class="w-8px h-8px mt-3 mr-1"
                :style="{
                  borderRadius: '50%',
                  backgroundColor:
                    AdoptStatusEnum[detail.adoptStatus as keyof typeof AdoptStatusEnum].color,
                }"
              ></div>
              {{
                detail.adoptStatus
                  ? AdoptStatusEnum[detail.adoptStatus as keyof typeof AdoptStatusEnum].label
                  : "-"
              }}
            </div>
          </div>
          <div class="result-item">
            <span class="label">采纳人员：</span>
            <span class="value">{{ detail.adoptBy ? detail.adoptBy : "-" }}</span>
          </div>
          <div class="result-item">
            <span class="label">采纳时间：</span>
            <div class="value">{{ dayjs(detail.submitTime).format("YYYY-MM-DD HH:mm") }}</div>
          </div>
        </div>
        <div v-if="detail.adoptStatus === 'REJECT'" class="form-item description-item">
          <span class="label">采纳意见：</span>
          <div class="value">{{ detail.adoptContent }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.issue-detail {
  min-height: 100vh;

  .section {
    margin-bottom: 16px;
    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 500;
      border-bottom: 1px solid #ebeef5;

      .title-left {
        display: flex;
        align-items: center;
        font-weight: bold;
      }

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 8px;
        background-color: #000;
        border-radius: 50%;
      }

      .return-btn {
        height: 28px;
        padding: 6px 16px;
        font-size: 12px;
      }
    }
  }

  .content-form,
  .handle-form,
  .handle-result {
    padding: 20px;
  }

  .result-item {
    display: flex;
    flex: 0 0 calc(30% - 10px);
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 32px;
  }

  .form-item {
    display: flex;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 32px;

    .value {
      flex: 1;

      :deep(.el-textarea) {
        width: 100%;

        .el-textarea__inner {
          font-size: 14px;
        }
      }
    }

    &.description-item {
      flex: 0 0 100%;
      align-items: flex-start;
    }
  }
  .label {
    flex-shrink: 0;
    color: #606266;
    text-align: left;

    &.required::before {
      margin-right: 4px;
      color: #f56c6c;
      content: "*";
    }
  }

  .form-row {
    display: flex;
    justify-content: space-between;

    .form-item {
      flex: 0 0 calc(50% - 10px);
    }
  }

  .form-actions {
    margin-top: 60px;
    text-align: right;

    .el-button {
      min-width: 100px;
      margin: 0 6px;
    }
  }

  :deep(.el-radio) {
    margin-right: 30px;
  }
}
</style>
