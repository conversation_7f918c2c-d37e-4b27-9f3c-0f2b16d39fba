-- 商会成员区分部门并单独记分
-- 影响记分的表需要添加对应的归属以便统计分数

-- 活动表
ALTER TABLE `tsz_activity` 
-- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '活动归属' AFTER `activity_type`;

-- 会议表
ALTER TABLE `tsz_meeting` 
-- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '会议归属' AFTER `meeting_type`;

-- 重点工作表
ALTER TABLE `tsz_key_work` 
-- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '重点工作归属' AFTER `work_type`;

-- 营商环境问题表
ALTER TABLE `tsz_problem` 
-- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '问题归属' AFTER `business_type`;

-- 意见建议表
ALTER TABLE `tsz_opinion` 
-- -- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '意见归属' AFTER `member_id`;

-- 评分记录表
ALTER TABLE `tsz_scoring_record` 
-- DROP COLUMN `category`,
ADD COLUMN `category` varchar(50)  NOT NULL COMMENT '评分归属' AFTER `scoring_type`,
ADD COLUMN `opinion_id` bigint DEFAULT NULL COMMENT '意见id' AFTER `problem_id`;

-- 新增数据 字典表
INSERT INTO `sys_dict` VALUES (7, 'scoring_category', '履职得分归类', 1, '履职得分归类（所属商会）', now(), 1, NULL, 1, 0);

-- 新增数据 字典数据表
INSERT INTO `sys_dict_data` VALUES (21, 'scoring_category', 'SHCY', '总商会', NULL, 1, 1, NULL, now(), 1, NULL, 1);
INSERT INTO `sys_dict_data` VALUES (22, 'scoring_category', 'XLHHY', '新联会', NULL, 1, 2, NULL, now(), 1, NULL, 1);
