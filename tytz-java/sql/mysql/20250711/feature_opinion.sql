-- 意见审核记录表
DROP TABLE IF EXISTS `tsz_opinion_audit_record`;
CREATE TABLE `tsz_opinion_audit_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `opinion_id` bigint NOT NULL COMMENT '意见ID',
  `audit_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核状态（SUBMITTED： 已提交, ADOPTED： 已采用, REJECTED： 未采用）',
  `audit_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核内容',
  `audit_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核人',
  `audit_time` datetime NOT NULL COMMENT '审核时间',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `fk_opinion_id` FOREIGN KEY (`opinion_id`) REFERENCES `tsz_opinion` (`id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '意见审核记录表' ROW_FORMAT = DYNAMIC;

-- 更新意见表
ALTER TABLE `tsz_opinion` 
ADD COLUMN `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '意见状态（SUBMITTED： 已提交, ADOPTED： 已采用, REJECTED： 未采用）' AFTER `member_id`;
-- 更新意见表默认数据
UPDATE `tsz_opinion` SET `status` = 'SUBMITTED' WHERE `status` = '';
