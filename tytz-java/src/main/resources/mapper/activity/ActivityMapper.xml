<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.activity.mapper.ActivityMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youlai.boot.modules.activity.model.entity.Activity">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="department" property="department"/>
        <result column="participants" property="participants"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="content" property="content"/>
        <result column="attachments" property="attachments"/>
        <result column="activity_type" property="activityType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
    <!-- 分页查询活动 -->
    <select id="getActivityPage" resultType="com.youlai.boot.modules.activity.model.vo.ActivityVO">
        SELECT
            a.id,
            a.title,
            a.department,
            a.participants,
            a.start_time,
            a.end_time,
            a.content,
            a.attachments,
            a.activity_type,
            a.category,
            a.create_time
        FROM
            tsz_activity a
        WHERE
            a.is_deleted = 0
        <if test="queryParams.title != null and queryParams.title != ''">
            AND a.title LIKE CONCAT('%', #{queryParams.title}, '%')
        </if>
        <if test="queryParams.activityType != null">
            AND a.activity_type = #{queryParams.activityType}
        </if>
        <if test="queryParams.startTime != null">
            AND a.start_time &lt;= #{queryParams.startTime}
        </if>
        <if test="queryParams.endTime != null">
            AND a.end_time &gt;= #{queryParams.endTime}
        </if>
        ORDER BY a.create_time DESC
    </select>
</mapper>