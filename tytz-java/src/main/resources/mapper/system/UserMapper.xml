<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.system.mapper.UserMapper">
    <!-- 用户分页列表 -->
    <select id="getUserPage" resultType="com.youlai.boot.system.model.bo.UserBO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.mobile,
            u.gender,
            u.avatar,
            u.STATUS,
            u.email,
            u.company,
            u.address,
            d.NAME AS dept_name,
            GROUP_CONCAT( r.NAME ) AS roleNames,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
                LEFT JOIN sys_user_role sur ON u.id = sur.user_id
                LEFT JOIN sys_role r ON sur.role_id = r.id
        <where>
            u.is_deleted = 0
            <!-- 超级管理员不显示在列表 -->
            AND r.name != '${@com.youlai.boot.common.constant.SystemConstants@ROOT_ROLE_CODE}'
            <if test='queryParams.keywords!=null and queryParams.keywords.trim() neq ""'>
                AND (
                    u.username LIKE CONCAT('%',#{queryParams.keywords},'%')
                    OR u.nickname LIKE CONCAT('%',#{queryParams.keywords},'%')
                    OR u.mobile LIKE CONCAT('%',#{queryParams.keywords},'%')
                    )
            </if>
            <if test='queryParams.status!=null'>
                AND u.status = #{queryParams.status}
            </if>
            <if test='queryParams.deptId!=null'>
                AND concat(',',concat(d.tree_path,',',d.id),',') like concat('%,',#{queryParams.deptId},',%')
            </if>
            <if test='queryParams.roleIds!=null and queryParams.roleIds.size > 0'>
                AND sur.role_id in
                <foreach collection="queryParams.roleIds" item="roleId" open="(" close=")" separator=",">
                    #{roleId}
                </foreach>
            </if>
            <if test="queryParams.createTime != null and queryParams.createTime.size > 0">
                <if test="queryParams.createTime[0] != null and queryParams.createTime[0] != ''">
                    <bind name="startDate" value="queryParams.createTime[0].length() == 10 ? queryParams.createTime[0] + ' 00:00:00' : queryParams.createTime[0]"/>
                    AND u.create_time &gt;= #{startDate}
                </if>
                <if test="queryParams.createTime[1] != null and queryParams.createTime[1] != ''">
                    <bind name="endDate" value="queryParams.createTime[1].length() == 10 ? queryParams.createTime[1] + ' 23:59:59' : queryParams.createTime[1]"/>
                    AND u.create_time &lt;= #{endDate}
                </if>
            </if>
            <if test="queryParams.roleIds != null and queryParams.roleIds.size() > 0">
                AND sur.role_id IN
                <foreach item="roleId" collection="queryParams.roleIds" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        GROUP BY
            u.id
        <choose>
            <!-- 如果排序参数都传入 -->
            <when test="queryParams.field != null and queryParams.field != '' and queryParams.direction != null">
                ORDER BY u.${queryParams.field} ${queryParams.direction}
            </when>
            <!-- 默认排序 -->
            <otherwise>
                ORDER BY u.update_time DESC, u.create_time DESC
            </otherwise>
        </choose>
    </select>
    <!-- 用户表单信息映射 -->
    <resultMap id="UserFormMap" type="com.youlai.boot.system.model.form.UserForm">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="TINYINT"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <collection
                property="roleIds"
                column="id"
                select="com.youlai.boot.system.mapper.UserRoleMapper.listRoleIdsByUserId" >
            <result column="role_id" />
        </collection>
    </resultMap>
    <!-- 根据用户ID获取用户详情 -->
    <select id="getUserFormData" resultMap="UserFormMap">
        SELECT
            id,
            username,
            nickname,
            mobile,
            gender,
            avatar,
            email,
            status,
            dept_id,
            company,
            address
        FROM
            sys_user
        WHERE
            id = #{userId} AND is_deleted = 0
    </select>
    <!-- 用户认证信息映射 -->
    <resultMap id="UserAuthMap" type="com.youlai.boot.system.model.dto.UserAuthInfo">
        <id property="userId" column="userId" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <collection property="roles" ofType="string" javaType="java.util.Set">
            <result column="code" />
        </collection>
    </resultMap>
    <!-- 根据用户名获取用户的认证信息 -->
    <select id="getUserAuthInfo" resultMap="UserAuthMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.PASSWORD,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.username = #{username} AND t1.is_deleted = 0
    </select>
    <!-- 根据微信openid获取用户的认证信息  -->
    <select id="getUserAuthInfoByOpenId" resultMap="UserAuthMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.PASSWORD,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.openid = #{openid} AND t1.is_deleted = 0
    </select>
    <!-- 根据手机号获取用户的认证信息 -->
    <select id="getUserAuthInfoByMobile" resultMap="UserAuthMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.mobile = #{mobile} AND t1.is_deleted = 0
    </select>
    <!-- 获取用户导出列表 -->
    <select id="listExportUsers" resultType="com.youlai.boot.system.model.dto.UserExportDTO">
        SELECT
            u.username,
            u.nickname,
            u.mobile,
            u.email,
            u.gender,
            u.company,
            u.address,
            d.NAME AS dept_name,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
        <where>
            u.is_deleted = 0
            <if test='keywords!=null and keywords.trim() neq ""'>
                AND (u.username LIKE CONCAT('%',#{keywords},'%')
                OR u.nickname LIKE CONCAT('%',#{keywords},'%')
                OR u.mobile LIKE CONCAT('%',#{keywords},'%'))
            </if>
            <if test='status!=null'>
                AND u.status = #{status}
            </if>
            <if test='deptId!=null'>
                AND concat(',',concat(d.tree_path,',',d.id),',') like concat('%,',#{deptId},',%')
            </if>
        </where>
        GROUP BY u.id
    </select>
    <!-- 根据用户ID获取用户详情 -->
    <select id="getUserProfile" resultType="com.youlai.boot.system.model.bo.UserBO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.mobile,
            u.gender,
            u.avatar,
            u.STATUS,
            u.email,
            u.company,
            u.address,
            d.NAME AS deptName,
            GROUP_CONCAT(r.NAME) AS roleNames,
            GROUP_CONCAT(r.id) AS roleId,
            GROUP_CONCAT(r.id,':',r.code,':',r.name ORDER BY r.id) AS rolesStr,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
                LEFT JOIN sys_user_role sur ON u.id = sur.user_id
                LEFT JOIN sys_role r ON sur.role_id = r.id AND r.is_deleted = 0
        WHERE
            u.id = #{userId} AND u.is_deleted = 0
        GROUP BY u.id
    </select>
    <select id="getUsersByDeptIds" resultType="com.youlai.boot.system.model.vo.UserPageVO">
        SELECT
        u.id,
        u.username,
        u.nickname,
        u.mobile,
        u.email,
        u.gender,
        u.avatar,
        u.status,
        u.dept_id,
        d.name as dept_name,
        u.create_time,
        u.company
        FROM
        sys_user u
        LEFT JOIN
        sys_dept d ON u.dept_id = d.id
        WHERE
        u.dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        ORDER BY
        u.create_time DESC
    </select>
</mapper>