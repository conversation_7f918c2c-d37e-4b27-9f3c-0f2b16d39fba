<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper">
    <!-- 获取评分记录分页列表 -->
    <select id="getScoringRecordPage" resultType="com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO">
    SELECT
        MAX(id) as id,
        year,
        member_id,
        member_name,
        department,
        category,
        COUNT(CASE WHEN scoring_type = 'ACTIVITY' THEN 1 ELSE NULL END) as activeCount,
        COUNT(CASE WHEN scoring_type = 'MEETING' THEN 1 ELSE NULL END) as meetingCount,
        COUNT(CASE WHEN scoring_type = 'KEY_WORK' THEN 1 ELSE NULL END) as keyWorkCount,
        COUNT(CASE WHEN scoring_type = 'ENVIRONMENT' THEN 1 ELSE NULL END) as environmentCount,
        COUNT(CASE WHEN scoring_type = 'OPINION' THEN 1 ELSE NULL END) AS opinionCount,
        SUM(score) as totalScore
    FROM
        tsz_scoring_record
        <where>
            is_deleted = 0
            AND COALESCE(NULLIF(category, ''), '') != ''
            <if test="queryParams.memberName != null and queryParams.memberName != ''">
                AND member_name LIKE CONCAT('%', #{queryParams.memberName}, '%')
            </if>
            <if test="queryParams.department != null and queryParams.department != ''">
                AND department LIKE CONCAT('%', #{queryParams.department}, '%')
            </if>
            <if test="queryParams.year != null and queryParams.year != ''">
                AND year = #{queryParams.year}
            </if>
            <if test="queryParams.category != null and queryParams.year != ''">
                AND category = #{queryParams.category}
            </if>
            <!-- <if test="queryParams.memberDeptCode != null and queryParams.memberDeptCode != ''">
                AND member_id IN (
                        SELECT sur.user_id 
                            FROM sys_role sr 
                            LEFT JOIN sys_user_role sur ON sur.role_id = sr.id 
                            WHERE sr.CODE = #{queryParams.memberDeptCode}
                        ) 
            </if> -->
        </where>
    GROUP BY year, member_id, category, member_name, department
    ORDER BY year DESC, totalScore DESC
    </select>
    <!-- 根据会议ID删除评分记录 -->
    <delete id="deleteByMeetingId">
        DELETE FROM tsz_scoring_record
        WHERE meeting_id = #{meetingId}
    </delete>
    <!-- 根据工作ID删除评分记录 -->
    <delete id="deleteByWorkId">
        DELETE FROM tsz_scoring_record
        WHERE work_id = #{workId}
    </delete>
    <!-- 根据活动ID删除评分记录 -->
    <delete id="deleteByActivityId">
        DELETE FROM tsz_scoring_record
        WHERE activity_id = #{activityId}
    </delete>
    <!-- 根据营商材料ID删除评分记录 -->
    <delete id="deleteByBusinessMaterialsId">
        DELETE FROM tsz_scoring_record
        WHERE business_materials_id = #{businessMaterialsId}
    </delete>
    <!-- 根据问题ID删除评分记录 -->
    <delete id="deleteByProblemId">
        DELETE FROM tsz_scoring_record
        WHERE problem_id = #{problemId}
    </delete>
    <!-- 根据意见ID删除评分记录 -->
    <delete id="deleteByOpinionId">
        DELETE FROM tsz_scoring_record
        WHERE opinion_id = #{opinionId}
    </delete>
    <!-- 获取评分记录导出列表（带统计数据） -->
    <select id="getExportList" resultType="com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO">
        SELECT
            MAX(id) as id,
            year,
            member_id,
            member_name,
            department,
            COUNT(CASE WHEN scoring_type = 'ACTIVITY' THEN 1 ELSE NULL END) as activeCount,
            COUNT(CASE WHEN scoring_type = 'MEETING' THEN 1 ELSE NULL END) as meetingCount,
            COUNT(CASE WHEN scoring_type = 'KEY_WORK' THEN 1 ELSE NULL END) as keyWorkCount,
            COUNT(CASE WHEN scoring_type = 'ENVIRONMENT' THEN 1 ELSE NULL END) as environmentCount,
            SUM(score) as totalScore
        FROM
            tsz_scoring_record
        <where>
            is_deleted = 0
            <if test="memberName != null and memberName != ''">
                AND member_name LIKE CONCAT('%', #{memberName}, '%')
            </if>
            <if test="department != null and department != ''">
                AND department LIKE CONCAT('%', #{department}, '%')
            </if>
            <if test="year != null and year != ''">
                AND year = #{year}
            </if>
            <if test="ids != null and ids != ''">
                AND id IN
                <foreach collection="ids.split(',')" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="memberDeptCode != null and memberDeptCode != ''">
                AND member_id IN (
                        SELECT sur.user_id 
                            FROM sys_role sr 
                            LEFT JOIN sys_user_role sur ON sur.role_id = sr.id 
                            WHERE sr.CODE = #{memberDeptCode}
                        ) 
            </if>
        </where>
        GROUP BY year, member_id, member_name, department
        ORDER BY year DESC, totalScore DESC
    </select>
</mapper>