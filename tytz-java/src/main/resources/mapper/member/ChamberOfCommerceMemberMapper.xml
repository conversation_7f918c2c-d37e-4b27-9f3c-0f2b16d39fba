<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper">
    <!-- 原有查询方法（保持兼容性）NOTE: 待优化，角色限定一下 -->
    <select id="getChamberOfCommerceMemberList" resultType="com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO">
        SELECT
        	su.id,
        	su.username,
        	su.nickname,
        	su.mobile,
        	su.gender,
        	su.avatar,
        	su.email,
        	su.status,
        	su.company
        FROM
        	sys_user su
        WHERE
            su.is_deleted = 0
            AND
        	su.status = 1
        <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND username LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
            AND su.id IN (
                SELECT sur.user_id
                    FROM sys_role sr
                    INNER JOIN sys_user_role sur ON sur.role_id = sr.id
                WHERE sr.is_deleted = 0
                    AND sr.status = 1
        <!-- 限定角色为总商会和新联会 -->
                    AND sr.CODE IN ('SHCY', 'XLHHY')
        <if test="queryParams.chamberOfCommerceCodes != null and queryParams.chamberOfCommerceCodes.length > 0">
                    AND sr.CODE IN
            <foreach collection="queryParams.chamberOfCommerceCodes" item="code" open="(" separator="," close=")">
                    #{code}
            </foreach>
        </if>
            )
        ORDER BY
	    	su.create_time DESC
    </select>
    <!-- 批量获取用户角色信息 -->
    <select id="getUserRolesByUserIds" resultType="com.youlai.boot.modules.member.model.bo.MemberRoleBo">
        SELECT
            ur.user_id as member_id,
            ur.role_id,
            r.name as role_name,
            r.code as role_code
        FROM sys_user_role ur
        INNER JOIN sys_role r ON ur.role_id = r.id
        WHERE r.is_deleted = 0
            AND r.status = 1
            AND ur.user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
        </foreach>
        ORDER BY ur.user_id, r.sort
    </select>
</mapper>