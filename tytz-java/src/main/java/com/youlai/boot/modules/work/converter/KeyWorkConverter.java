package com.youlai.boot.modules.work.converter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * 年度重点工作对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper(componentModel = "spring")
public interface KeyWorkConverter {
    /**
     * 实体转换为表单对象
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "stringToCategory")
    KeyWorkForm toForm(KeyWork entity);

    /**
     * 表单对象转换为实体
     *
     * @param formData 表单对象
     * @return 实体对象
     */
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "categoryToString")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    KeyWork toEntity(KeyWorkForm formData);

    /**
     * 将分类枚举列表转换为字符串
     *
     * @param category 分类枚举列表
     * @return 逗号分隔的字符串
     */
    @Named("categoryToString")
    default String categoryToString(List<ScoringCategoryEnum> category) {
        if (category == null || category.isEmpty()) {
            return null;
        }
        return category.stream().map(ScoringCategoryEnum::name).collect(Collectors.joining(","));
    }

    /**
     * 将字符串转换为分类枚举列表
     *
     * @param category 逗号分隔的字符串
     * @return 分类枚举列表
     */
    @Named("stringToCategory")
    default List<ScoringCategoryEnum> stringToCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return null;
        }
        return Arrays.stream(category.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(ScoringCategoryEnum::valueOf)
                .collect(Collectors.toList());
    }
}