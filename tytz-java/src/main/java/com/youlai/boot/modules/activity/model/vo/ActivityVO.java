package com.youlai.boot.modules.activity.model.vo;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.youlai.boot.common.enums.ActivityTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 活动管理视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema(description = "活动管理视图对象")
public class ActivityVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "活动名称")
    private String title;

    @Schema(description = "活动类型")
    private ActivityTypeEnum activityType;

    @Schema(description = "活动归类")
    private String category;

    @Schema(description = "主办单位")
    private String department;

    @Schema(description = "参与人员JSON数据")
    private String participants;

    @Schema(description = "活动开始时间")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    @Schema(description = "活动内容")
    private String content;

    @Schema(description = "附件JSON数据")
    private String attachments;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}