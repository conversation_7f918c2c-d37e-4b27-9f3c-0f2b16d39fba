package com.youlai.boot.modules.activity.converter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.activity.model.form.ActivityForm;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * 活动管理对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper(componentModel = "spring")
public interface ActivityConverter {
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "string2Category")
    ActivityForm toForm(Activity entity);

    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "category2String")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "department", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Activity toEntity(ActivityForm formData);

    /**
     * 将枚举列表转换为字符串
     *
     * @param categoryList 枚举列表
     * @return 逗号分隔的字符串
     */
    @Named("category2String")
    static String category2String(List<ScoringCategoryEnum> categoryList) {
        if (categoryList == null || categoryList.isEmpty()) {
            return null;
        }
        return categoryList.stream()
                .map(ScoringCategoryEnum::getValue)
                .collect(Collectors.joining(","));
    }

    /**
     * 将字符串转换为枚举列表
     *
     * @param categoryStr 逗号分隔的字符串
     * @return 枚举列表
     */
    @Named("string2Category")
    static List<ScoringCategoryEnum> string2Category(String categoryStr) {
        if (categoryStr == null || categoryStr.trim().isEmpty()) {
            return null;
        }
        return Arrays.stream(categoryStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(value -> {
                    // 根据value值查找对应的枚举
                    for (ScoringCategoryEnum category : ScoringCategoryEnum.values()) {
                        if (category.getValue().equals(value)) {
                            return category;
                        }
                    }
                    // 如果找不到匹配的value，尝试使用name()方法
                    return ScoringCategoryEnum.valueOf(value);
                })
                .collect(Collectors.toList());
    }
}