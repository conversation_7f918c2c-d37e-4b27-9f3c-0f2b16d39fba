package com.youlai.boot.modules.problem.model.entity;

import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;

import lombok.Data;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;

/**
 * 营商环境问题实体对象
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@TableName("tsz_problem")
public class Problem extends BaseEntity {

    /**
     * 营商环境标题
     */
    private String title;

    /**
     * 营商环境问题内容
     */
    private String content;

    /**
     * 营商环境类别
     */
    private String businessType;

    /**
     * 问题归属
     */
    private ScoringCategoryEnum category;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 商会会员名称
     */
    private String memberName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 采纳状态
     */
    private AdoptStatusEnum adoptStatus;

    /**
     * 采纳意见
     */
    private String adoptContent;

    /**
     * 采纳人
     */
    private String adoptBy;

    /**
     * 采纳时间
     */
    private LocalDateTime adoptTime;

    /**
     * 批示状态
     */
    private InstructionStatusEnum instructionStatus;

    /**
     * 批示内容
     */
    private String instructionContent;

    /**
     * 批示人
     */
    private String instructionBy;

    /**
     * 批示时间
     */
    private LocalDateTime instructionTime;

    /**
     * 领导批示
     */
    private String leaderInstruction;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;

    /**
     * 商会成员id
     */
    private Long memberId;

    /**
     * 会员身份
     */
    private String memberRole;
}