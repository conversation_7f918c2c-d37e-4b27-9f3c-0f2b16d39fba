package com.youlai.boot.modules.opinion.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.youlai.boot.common.enums.ScoringCategoryEnum;

/**
 * 意见征集视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Schema(description = "意见征集视图对象")
@Data
public class OpinionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "意见内容")
    private String content;

    @Schema(description = "商会会员名称")
    private String memberName;

    @Schema(description = "所属单位")
    private String department;

    @Schema(description = "意见归属")
    private ScoringCategoryEnum category;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}