package com.youlai.boot.modules.scoringrecord.model.vo;

import com.youlai.boot.common.enums.ScoreTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评分记录详情VO
 */
@Data
@Schema(name = "评分记录详情VO")
public class ScoringRecordDetailVO {

    /**
     * 主键
     */
    @Schema(name = "主键")
    private Long id;

    /**
     * 年度
     */
    @Schema(name = "年度")
    private String year;

    /**
     * 商会会员名称
     */
    @Schema(name = "商会会员名称")
    private String memberName;

    /**
     * 所属部门
     */
    @Schema(name = "所属部门")
    private String department;

    /**
     * 评分类型
     */
    @Schema(name = "评分类型")
    private ScoreTypeEnum scoringType;

    /**
     * 评分类型名称
     */
    @Schema(name = "评分类型名称")
    private String scoringTypeName;

    /**
     * 评分详细内容
     */
    @Schema(name = "评分详细内容")
    private String scoringDetail;

    /**
     * 得分
     */
    @Schema(name = "得分")
    private Long score;

    /**
     * 创建时间
     */
    @Schema(name = "创建时间")
    private LocalDateTime createTime;

    /**
     * 活动ID
     */
    @Schema(name = "活动ID")
    private Long activityId;

    /**
     * 活动名称
     */
    @Schema(name = "活动名称")
    private String activityName;

    /**
     * 活动类型
     */
    @Schema(name = "活动类型")
    private String activityType;

    /**
     * 会议ID
     */
    @Schema(name = "会议ID")
    private Long meetingId;

    /**
     * 会议名称
     */
    @Schema(name = "会议名称")
    private String meetingName;

    /**
     * 会议类型
     */
    @Schema(name = "会议类型")
    private String meetingType;

    /**
     * 工作ID
     */
    @Schema(name = "工作ID")
    private Long workId;

    /**
     * 工作名称
     */
    @Schema(name = "工作名称")
    private String workName;

    /**
     * 工作类型
     */
    @Schema(name = "工作类型")
    private String workType;

    /**
     * 问题ID
     */
    @Schema(name = "问题ID")
    private Long problemId;

    /**
     * 问题标题
     */
    @Schema(name = "问题标题")
    private String problemTitle;

    /**
     * 意见ID
     */
    @Schema(name = "意见ID")
    private Long opinionId;

    /**
     * 意见内容
     */
    @Schema(name = "意见内容")
    private String opinionContent;
}
