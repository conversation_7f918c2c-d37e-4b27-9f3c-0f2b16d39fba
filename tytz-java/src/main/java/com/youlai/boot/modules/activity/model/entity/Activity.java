package com.youlai.boot.modules.activity.model.entity;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.ActivityTypeEnum;

/**
 * 活动管理实体对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@TableName("tsz_activity")
public class Activity extends BaseEntity {

    /**
     * 活动名称
     */
    private String title;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 活动归类(多个归类使用英文逗号(,)分割)
     */
    private String category;

    /**
     * 主办单位
     */
    private String department;

    /**
     * 参与人员JSON数据
     * participants: "[{\"id\":1462},{\"id\":1463},{\"id\":1459},{\"id\":1103}]"
     */
    private String participants;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动内容
     */
    private String content;

    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}