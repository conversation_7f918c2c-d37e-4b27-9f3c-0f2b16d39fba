package com.youlai.boot.modules.meeting.model.vo;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.youlai.boot.common.enums.MeetingTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 会议管理视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema(description = "会议管理视图对象")
public class MeetingVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "会议ID")
    private Long id;

    @Schema(description = "会议名称")
    private String title;

    @Schema(description = "会议类型")
    private MeetingTypeEnum meetingType;

    @Schema(description = "会议归类")
    private String category;

    @Schema(description = "组织者")
    private String department;

    @Schema(description = "参会人员JSON数据")
    private String participants;

    @Schema(description = "会议开始时间")
    private LocalDateTime startTime;

    @Schema(description = "会议结束时间")
    private LocalDateTime endTime;

    @Schema(description = "会议内容")
    private String content;

    @Schema(description = "附件JSON数据")
    private String attachments;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}