package com.youlai.boot.modules.scoringrecord.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.activity.service.ActivityService;
import com.youlai.boot.modules.meeting.service.MeetingService;
import com.youlai.boot.modules.opinion.service.OpinionService;
import com.youlai.boot.modules.problem.model.query.ProblemQuery;
import com.youlai.boot.modules.problem.model.vo.ProblemExcelVO;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import com.youlai.boot.modules.problem.service.ProblemService;
import com.youlai.boot.modules.scoringrecord.model.dto.ScoringRecordDTO;
import com.youlai.boot.modules.scoringrecord.model.form.ScoringRecordForm;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordDetailQuery;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordPageQuery;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordDetailVO;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordExportVO;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO;
import com.youlai.boot.modules.scoringrecord.service.ScoringRecordService;
import com.youlai.boot.modules.work.service.KeyWorkService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评分记录控制器
 */
@Tag(name = "评分记录接口")
@RestController
@RequestMapping("/api/v1/scoring-records")
@RequiredArgsConstructor
public class ScoringRecordController {

    private final ScoringRecordService scoringRecordService;

    private final ActivityService activityService;
    private final MeetingService meetingService;
    private final KeyWorkService keyWorkService;
    private final ProblemService problemService;
    private final OpinionService opinionService;

    @Operation(summary = "获取评分记录分页列表")
    @GetMapping("/page")
    public PageResult<ScoringRecordVO> getScoringRecordPage(
            @Validated ScoringRecordPageQuery queryParams) {
        // 调用服务层方法获取评分记录分页数据
        // 返回的数据已按照年度和会员ID分组
        // 包含各类型活动的参与次数和总分
        // 按年度降序和总分降序排序
        Page<ScoringRecordVO> page = scoringRecordService.getScoringRecordPage(queryParams);
        return PageResult.success(page);
    }

    @Operation(summary = "获取评分记录详情")
    @GetMapping("/{id}")
    public Result<ScoringRecordDTO> getScoringRecordDetail(
            @Parameter(description = "评分记录ID") @PathVariable Long id) {
        ScoringRecordDTO scoringRecordDTO = scoringRecordService.getScoringRecordDetail(id);
        return Result.success(scoringRecordDTO);
    }

    @Operation(summary = "新增评分记录")
    @PostMapping
    public Result<Boolean> saveScoringRecord(
            @RequestBody @Validated ScoringRecordForm form) {
        boolean result = scoringRecordService.saveScoringRecord(form);
        return Result.judge(result);
    }

    @Operation(summary = "修改评分记录")
    @PutMapping("/{id}")
    public Result<Boolean> updateScoringRecord(
            @Parameter(description = "评分记录ID") @PathVariable Long id,
            @RequestBody @Validated ScoringRecordForm form) {
        boolean result = scoringRecordService.updateScoringRecord(id, form);
        return Result.judge(result);
    }

    @Operation(summary = "删除评分记录")
    @DeleteMapping("/{ids}")
    public Result<Boolean> deleteScoringRecords(
            @Parameter(description = "评分记录ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = scoringRecordService.deleteScoringRecords(ids);
        return Result.judge(result);
    }

    @Operation(summary = "分页获取评分记录详情")
    @GetMapping("/detail/page")
    public PageResult<ScoringRecordDetailVO> getScoringRecordDetailPage(
            @Validated ScoringRecordDetailQuery queryParams) {
        // 调用服务层方法获取评分记录详情分页数据
        // 支持按年度、会员ID和评分类型进行筛选
        // 详细信息包含当前活动的活动名称，选择的活动类型、当前活动获得的评分
        Page<ScoringRecordDetailVO> page = scoringRecordService.getScoringRecordDetailPage(queryParams);
        return PageResult.success(page);
    }

    @Operation(summary = "获取评分记录导出数据")
    @PostMapping("/export")
    public void exportScorceRecord(HttpServletResponse response, @RequestBody ScoringRecordPageQuery queryParams)
            throws IOException {
        // 设置响应头
        String fileName = "评分表管理数据.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        // 获取数据
        List<ScoringRecordVO> list = scoringRecordService.getExportList(queryParams);
        List<ScoringRecordExportVO> excelVOList = list.stream()
                .map(item -> {
                    ScoringRecordExportVO excelVO = new ScoringRecordExportVO();
                    BeanUtils.copyProperties(item, excelVO);
                    excelVO.setIndex(Long.valueOf(list.indexOf(item) + 1));
                    return excelVO;
                }).collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), ScoringRecordExportVO.class)
                .sheet("评分表管理数据")
                .doWrite(excelVOList);
    }

    /**
     * 更新原先的旧数据
     */
    @Operation(summary = "更新旧数据")
    @GetMapping("/update-old-data")
    @PreAuthorize("@ss.hasPerm('scoring:update:old:data')")
    public Result<Boolean> updateOldData() {
        // 更新营商环境问题表数据
        problemService.updateOldData();

        // 更新意见征集表数据
        opinionService.updateOldData();

        // 更新活动信息
        activityService.updateOldData();
        // 更新会议信息
        meetingService.updateOldData();
        // 更新重点工作信息
        keyWorkService.updateOldData();
        return Result.judge(true);
    }
}