package com.youlai.boot.modules.meeting.converter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.meeting.model.form.MeetingForm;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * 会议管理对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper(componentModel = "spring")
public interface MeetingConverter {
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "stringToCategory")
    MeetingForm toForm(Meeting entity);

    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "category", qualifiedByName = "categoryToString")
    Meeting toEntity(MeetingForm formData);

    @Named("categoryToString")
    static String categoryToString(List<ScoringCategoryEnum> category) {
        return category.stream().map(ScoringCategoryEnum::name).collect(Collectors.joining(","));
    }

    @Named("stringToCategory")
    static List<ScoringCategoryEnum> stringToCategory(String category) {
        return Arrays.stream(category.split(",")).map(ScoringCategoryEnum::valueOf).collect(Collectors.toList());
    }
}