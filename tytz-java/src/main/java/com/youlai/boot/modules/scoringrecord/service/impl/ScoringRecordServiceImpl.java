package com.youlai.boot.modules.scoringrecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.activity.service.ActivityService;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.meeting.service.MeetingService;
import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.modules.opinion.service.OpinionService;
import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.problem.service.ProblemService;
import com.youlai.boot.modules.scoringrecord.converter.ScoringRecordConverter;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.modules.scoringrecord.model.dto.ScoringRecordDTO;
import com.youlai.boot.modules.scoringrecord.model.form.ScoringRecordForm;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordDetailQuery;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordPageQuery;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordDetailVO;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO;
import com.youlai.boot.modules.scoringrecord.service.ScoringRecordService;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.service.KeyWorkService;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 评分记录服务实现类
 */
@Service
@RequiredArgsConstructor
public class ScoringRecordServiceImpl extends ServiceImpl<ScoringRecordMapper, ScoringRecord>
        implements ScoringRecordService {

    private final ScoringRecordConverter scoringRecordConverter;
    private final ActivityService activityService;
    private final MeetingService meetingService;
    private final KeyWorkService keyWorkService;
    private final ProblemService problemService;
    private final OpinionService opinionService;
    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    /**
     * 获取评分记录分页列表
     *
     * @param queryParams 查询参数
     * @return 评分记录分页数据
     */
    @Override
    public Page<ScoringRecordVO> getScoringRecordPage(ScoringRecordPageQuery queryParams) {
        // 查询数据
        Page<ScoringRecord> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
        Page<ScoringRecordVO> recordPage = this.baseMapper.getScoringRecordPage(page, queryParams);
        // 翻译得分归类
        for (ScoringRecordVO record : recordPage.getRecords()) {
            // record.setCategory(ScoringCategoryEnum.getEnumByValue(record.getCategory().getLabel()));
            record.setCategoryLabel(record.getCategory().getLabel());
        }

        // 查询会员所属商会信息
        if (!recordPage.getRecords().isEmpty()) {
            Set<Long> memberIds = recordPage.getRecords().stream()
                    .map(ScoringRecordVO::getMemberId)
                    .collect(Collectors.toSet());

            // 批量查询所有用户的角色信息
            Set<String> userIds = memberIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet());
            List<MemberRoleBo> userRoleMappings = chamberOfCommerceMemberMapper
                    .getUserRolesByUserIds(userIds);

            // 按用户ID分组角色信息
            Map<String, List<ChamberOfCommerceMemberVO.RoleInfo>> userRolesMap = userRoleMappings.stream()
                    .collect(Collectors.groupingBy(
                            MemberRoleBo::getMemberId,
                            Collectors.mapping(mapping -> {
                                ChamberOfCommerceMemberVO.RoleInfo roleInfo = new ChamberOfCommerceMemberVO.RoleInfo();
                                roleInfo.setRoleId(mapping.getRoleId());
                                roleInfo.setRoleName(mapping.getRoleName());
                                roleInfo.setRoleCode(mapping.getRoleCode());
                                return roleInfo;
                            }, Collectors.toList())));

            for (ScoringRecordVO record : recordPage.getRecords()) {
                String memberDeptName = userRolesMap.get(String.valueOf(record.getMemberId()))
                        .stream()
                        .map(ChamberOfCommerceMemberVO.RoleInfo::getRoleName)
                        .collect(Collectors.joining("、"));
                record.setMemberDeptName(memberDeptName);
            }
        }

        // 返回按照年度和会员ID分组的评分记录
        // 包含各类型活动的参与次数和总分
        // 按年度降序和总分降序排序
        // SQL已经在mapper中处理了分组和计算逻辑
        return recordPage;
    }

    /**
     * 获取评分记录详情
     */
    @Override
    public ScoringRecordDTO getScoringRecordDetail(Long id) {
        ScoringRecord record = this.getById(id);
        Assert.notNull(record, "评分记录不存在");
        return scoringRecordConverter.entityToDTO(record);
    }

    /**
     * 新增评分记录
     */
    @Override
    public boolean saveScoringRecord(ScoringRecordForm form) {
        ScoringRecord entity = scoringRecordConverter.formToEntity(form);
        return this.save(entity);
    }

    /**
     * 修改评分记录
     */
    @Override
    public boolean updateScoringRecord(Long id, ScoringRecordForm form) {
        // 校验存在性
        ScoringRecord record = this.getById(id);
        Assert.notNull(record, "评分记录不存在");

        // 修改记录
        ScoringRecord entity = scoringRecordConverter.formToEntity(form);
        entity.setId(id);
        return this.updateById(entity);
    }

    /**
     * 删除评分记录
     */
    @Override
    public boolean deleteScoringRecords(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的评分记录ID不能为空");
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        return this.removeByIds(idList);
    }

    /**
     * 根据查询条件分页获取评分记录详情
     * 支持按年度、会员ID和评分类型进行筛选
     */
    @Override
    public Page<ScoringRecordDetailVO> getScoringRecordDetailPage(ScoringRecordDetailQuery queryParams) {
        // 创建分页对象
        Page<ScoringRecord> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<ScoringRecord> queryWrapper = new LambdaQueryWrapper<ScoringRecord>()
                .eq(StrUtil.isNotBlank(queryParams.getYear()), ScoringRecord::getYear, queryParams.getYear())
                .eq(queryParams.getMemberId() != null, ScoringRecord::getMemberId, queryParams.getMemberId())
                .eq(queryParams.getScoringType() != null, ScoringRecord::getScoringType, queryParams.getScoringType())
                .eq(queryParams.getCategory() != null, ScoringRecord::getCategory, queryParams.getCategory().getValue())
                .eq(ScoringRecord::getIsDeleted, 0)
                .orderByDesc(ScoringRecord::getCreateTime);

        // 执行分页查询
        Page<ScoringRecord> recordPage = this.page(page, queryWrapper);
        List<ScoringRecord> records = recordPage.getRecords();

        if (records.isEmpty()) {
            return new Page<>();
        }

        // 收集所有的ID以便于批量查询
        Set<Long> activityIds = new HashSet<>();
        Set<Long> meetingIds = new HashSet<>();
        Set<Long> workIds = new HashSet<>();
        Set<Long> problemIds = new HashSet<>();
        Set<Long> opinionIds = new HashSet<>();

        for (ScoringRecord record : records) {
            if (record.getActivityId() != null) {
                activityIds.add(record.getActivityId());
            }
            if (record.getMeetingId() != null) {
                meetingIds.add(record.getMeetingId());
            }
            if (record.getWorkId() != null) {
                workIds.add(record.getWorkId());
            }
            if (record.getProblemId() != null) {
                problemIds.add(record.getProblemId());
            }
            if (record.getOpinionId() != null) {
                opinionIds.add(record.getOpinionId());
            }
        }

        // 批量查询相关实体
        Map<Long, Activity> activityMap = new HashMap<>();
        if (!activityIds.isEmpty()) {
            List<Activity> activities = activityService.listByIds(activityIds);
            activityMap = activities.stream().collect(Collectors.toMap(Activity::getId, activity -> activity));
        }

        Map<Long, Meeting> meetingMap = new HashMap<>();
        if (!meetingIds.isEmpty()) {
            List<Meeting> meetings = meetingService.listByIds(meetingIds);
            meetingMap = meetings.stream().collect(Collectors.toMap(Meeting::getId, meeting -> meeting));
        }

        Map<Long, KeyWork> workMap = new HashMap<>();
        if (!workIds.isEmpty()) {
            List<KeyWork> works = keyWorkService.listByIds(workIds);
            workMap = works.stream().collect(Collectors.toMap(KeyWork::getId, work -> work));
        }

        Map<Long, Problem> problemMap = new HashMap<>();
        if (!problemIds.isEmpty()) {
            List<Problem> problems = problemService.listByIds(problemIds);
            problemMap = problems.stream().collect(Collectors.toMap(Problem::getId, problem -> problem));
        }

        Map<Long, Opinion> opinionMap = new HashMap<>();
        if (!opinionIds.isEmpty()) {
            List<Opinion> opinions = opinionService.listByIds(opinionIds);
            opinionMap = opinions.stream().collect(Collectors.toMap(Opinion::getId, opinion -> opinion));
        }

        // 构建详细VO列表
        List<ScoringRecordDetailVO> detailVOs = new ArrayList<>();
        for (ScoringRecord record : records) {
            ScoringRecordDetailVO detailVO = new ScoringRecordDetailVO();

            // 复制基本属性
            BeanUtil.copyProperties(record, detailVO);

            // 设置评分类型名称
            if (record.getScoringType() != null) {
                detailVO.setScoringTypeName(record.getScoringType().getLabel());
            }

            // 根据不同类型设置相关实体信息
            if (record.getActivityId() != null) {
                Activity activity = activityMap.get(record.getActivityId());
                if (activity != null) {
                    detailVO.setActivityName(activity.getTitle());
                    if (activity.getActivityType() != null) {
                        detailVO.setActivityType(activity.getActivityType().getLabel());
                    }
                }
            }

            if (record.getMeetingId() != null) {
                Meeting meeting = meetingMap.get(record.getMeetingId());
                if (meeting != null) {
                    detailVO.setMeetingName(meeting.getTitle());
                    if (meeting.getMeetingType() != null) {
                        detailVO.setMeetingType(meeting.getMeetingType().getLabel());
                    }
                }
            }

            if (record.getWorkId() != null) {
                KeyWork work = workMap.get(record.getWorkId());
                if (work != null) {
                    detailVO.setWorkName(work.getWorkName());
                    if (work.getWorkType() != null) {
                        detailVO.setWorkType(work.getWorkType().getLabel());
                    }
                }
            }

            if (record.getProblemId() != null) {
                Problem problem = problemMap.get(record.getProblemId());
                if (problem != null) {
                    detailVO.setProblemTitle(problem.getTitle());
                }
            }

            if (record.getOpinionId() != null) {
                Opinion opinion = opinionMap.get(record.getOpinionId());
                if (opinion != null) {
                    detailVO.setOpinionContent(opinion.getContent());
                }
            }

            detailVOs.add(detailVO);
        }

        // 构建返回的分页对象
        Page<ScoringRecordDetailVO> resultPage = new Page<>(recordPage.getCurrent(), recordPage.getSize(),
                recordPage.getTotal());
        resultPage.setRecords(detailVOs);

        return resultPage;
    }

    /**
     * 获取评分记录导出数据
     * 支持根据逗号分隔的ID导出、根据查询条件导出或导出全部记录
     */
    @Override
    public List<ScoringRecordVO> getExportList(ScoringRecordPageQuery queryParams) {
        // 直接使用Mapper中的方法获取带统计数据的评分记录列表
        // 该方法会根据年度和会员ID分组，并计算各类型活动的参与次数和总分
        return this.baseMapper.getExportList(queryParams);
    }

}