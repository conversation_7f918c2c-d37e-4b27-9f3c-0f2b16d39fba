package com.youlai.boot.modules.opinion.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.common.util.UserInfoUtils;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.opinion.converter.OpinionConverter;
import com.youlai.boot.modules.opinion.mapper.OpinionMapper;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.modules.opinion.model.form.OpinionForm;
import com.youlai.boot.modules.opinion.model.query.OpinionQuery;
import com.youlai.boot.modules.opinion.model.query.MyOpinionQuery;
import com.youlai.boot.modules.opinion.model.vo.OpinionVO;
import com.youlai.boot.modules.opinion.service.OpinionService;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.modules.scoringrecord.service.ScoringRecordService;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.model.vo.UserProfileVO;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 意见征集服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@RequiredArgsConstructor
public class OpinionServiceImpl extends ServiceImpl<OpinionMapper, Opinion> implements OpinionService {

    private final OpinionConverter opinionConverter;
    private final UserMapper userMapper;
    private final ScoringRecordMapper scoringRecordMapper;

    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    @Override
    public IPage<OpinionVO> getOpinionPage(OpinionQuery queryParams) {
        // 查询参数
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        String memberName = queryParams.getMemberName();

        // 构建分页对象
        Page<Opinion> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Opinion> queryWrapper = new LambdaQueryWrapper<Opinion>()
                .like(StrUtil.isNotBlank(memberName), Opinion::getMemberName, memberName)
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Opinion::getCreateTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Opinion::getCreateTime, queryParams.getEndTime())
                .orderByDesc(Opinion::getCreateTime);

        // 查询数据
        Page<Opinion> opinionPage = this.page(page, queryWrapper);

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(opinionPage.getRecords());
        // 实体转换
        return opinionConverter.entity2Page(opinionPage);
    }

    @Override
    public IPage<OpinionVO> getMyOpinionPage(MyOpinionQuery queryParams) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();

        // 构建分页对象
        Page<Opinion> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<Opinion> queryWrapper = new LambdaQueryWrapper<Opinion>()
                .eq(Opinion::getCreateBy, userId)
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Opinion::getCreateTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Opinion::getCreateTime, queryParams.getEndTime())
                .like(StrUtil.isNotBlank(queryParams.getKeyword()), Opinion::getContent, queryParams.getKeyword())
                .orderByDesc(Opinion::getCreateTime);

        // 查询数据
        Page<Opinion> opinionPage = this.page(page, queryWrapper);

        // 实体转换
        return opinionConverter.entity2Page(opinionPage);
    }

    @Override
    public OpinionForm getOpinionFormData(Long id) {
        Opinion entity = this.getById(id);
        Assert.notNull(entity, "意见征集不存在");
        // 根据MemberId查询用户信息
        if (entity.getMemberId() != null) {
            User user = userMapper.selectById(entity.getMemberId());
            if (user != null) {
                entity.setMemberName(user.getNickname());
                entity.setDepartment(user.getCompany());
            }
        }
        return opinionConverter.entity2Form(entity);
    }

    @Override
    public boolean saveOpinion(OpinionForm formData) {
        Opinion entity = opinionConverter.form2Entity(formData);

        UserProfileVO userInfo = UserInfoUtils.getUserInfo();

        entity.setMemberName(userInfo.getNickname());
        entity.setMemberId(userInfo.getId());
        entity.setContact(userInfo.getMobile());
        entity.setDepartment(userInfo.getDeptName());

        // 设置创建人ID
        entity.setCreateBy(SecurityUtils.getUserId());

        // 先保存然后拿到对应的id
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 记分表插入数据
        ScoringRecord scoringRecord = new ScoringRecord();
        scoringRecord.setMemberId(entity.getMemberId());
        scoringRecord.setMemberName(entity.getMemberName());
        scoringRecord.setDepartment(entity.getDepartment());
        scoringRecord.setScoringType(ScoreTypeEnum.OPINION);
        scoringRecord.setScoringDetail(entity.getContent());
        // TODO: 20250703 目前没有做审核与采纳，所以只有1分
        scoringRecord.setScore(1L);
        scoringRecord.setCreateBy(SecurityUtils.getUserId());
        scoringRecord.setCreateTime(LocalDateTime.now());
        scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
        scoringRecord.setCategory(formData.getCategory().getValue());
        scoringRecord.setOpinionId(entity.getId());
        scoringRecordMapper.insert(scoringRecord);

        return true;
    }

    @Override
    public boolean updateOpinion(Long id, OpinionForm formData) {
        Opinion entity = this.getById(id);
        Assert.notNull(entity, "意见征集不存在");

        // 表单数据转换为实体
        Opinion opinion = opinionConverter.form2Entity(formData);
        opinion.setId(id);
        opinion.setUpdateBy(SecurityUtils.getUserId());
        opinion.setUpdateTime(LocalDateTime.now());

        // 删除记分表对应数据
        scoringRecordMapper.deleteByOpinionId(id);
        // 重新插入
        ScoringRecord scoringRecord = new ScoringRecord();
        scoringRecord.setMemberId(entity.getMemberId());
        scoringRecord.setMemberName(entity.getMemberName());
        scoringRecord.setDepartment(entity.getDepartment());
        scoringRecord.setScoringType(ScoreTypeEnum.OPINION);
        scoringRecord.setScoringDetail(entity.getContent());
        // TODO: 20250703 目前没有做审核与采纳，所以只有1分
        scoringRecord.setScore(1L);
        scoringRecord.setCreateBy(SecurityUtils.getUserId());
        scoringRecord.setCreateTime(LocalDateTime.now());
        scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
        scoringRecord.setCategory(formData.getCategory().getValue());
        scoringRecord.setOpinionId(entity.getId());
        scoringRecordMapper.insert(scoringRecord);

        return this.updateById(opinion);
    }

    @Override
    public boolean deleteOpinions(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的意见征集ID不能为空");

        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByOpinionId(id);
        }

        return this.removeByIds(idList);
    }

    @Override
    public List<OpinionVO> getExportList(OpinionQuery queryParams) {
        List<Opinion> opinionList;

        // 如果提供了ID参数，则根据ID查询
        if (StrUtil.isNotBlank(queryParams.getIds())) {
            // 将逗号分隔的ID字符串转换为集合
            List<Long> idList = Arrays.stream(queryParams.getIds().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 根据ID集合查询
            opinionList = this.listByIds(idList);
        } else {
            // 检查是否有其他查询条件
            boolean hasConditions = StrUtil.isNotBlank(queryParams.getMemberName()) ||
                    StrUtil.isNotBlank(queryParams.getStartTime()) ||
                    StrUtil.isNotBlank(queryParams.getEndTime());

            if (hasConditions) {
                // 有查询条件，根据条件查询
                LambdaQueryWrapper<Opinion> wrapper = new LambdaQueryWrapper<Opinion>()
                        .like(StrUtil.isNotBlank(queryParams.getMemberName()), Opinion::getMemberName,
                                queryParams.getMemberName())
                        .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Opinion::getCreateTime,
                                queryParams.getStartTime())
                        .le(StrUtil.isNotBlank(queryParams.getEndTime()), Opinion::getCreateTime,
                                queryParams.getEndTime())
                        .orderByDesc(Opinion::getCreateTime);

                opinionList = this.list(wrapper);
            } else {
                // 没有查询条件，导出全部数据
                LambdaQueryWrapper<Opinion> wrapper = new LambdaQueryWrapper<Opinion>()
                        .orderByDesc(Opinion::getCreateTime);

                opinionList = this.list(wrapper);
            }
        }

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(opinionList);

        // 转换为VO并返回
        return opinionList.stream()
                .map(opinionConverter::entity2Vo)
                .collect(Collectors.toList());
    }

    /**
     * 根据MemberId查询用户信息并设置MemberName和Department
     * 无论实体中是否已经有值，都会使用从用户表中查询到的最新信息
     *
     * @param voList 意见VO列表
     */
    private void setMemberInfo(List<Opinion> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }

        // 遍历每个VO
        for (Opinion vo : voList) {
            try {
                // 直接使用memberId
                Long memberId = null;
                if (vo.getMemberId() != null) {
                    memberId = (vo.getMemberId());
                }

                // 如果memberId为空，则不进行查询，直接使用数据库中已存储的值
                if (memberId == null) {
                    continue; // 跳过当前循环，不进行用户信息的查询
                }

                // 查询用户信息
                UserBO userBO = userMapper.getUserProfile(memberId);

                if (userBO != null) {
                    // 强制覆盖实体中的值，使用从用户表中查询到的最新信息
                    vo.setMemberName(userBO.getNickname()); // 使用昵称作为会员名称
                    vo.setDepartment(userBO.getCompany()); // 使用企业单位名称作为部门
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }

    @Override
    public boolean updateOldData() {
        // 1. 更新意见征集表数据
        // 获取所有category为null或者空字符串的的意见征集表数据
        List<Opinion> opinions = this.list(new LambdaQueryWrapper<Opinion>()
                .isNull(Opinion::getCategory)
                .or()
                .eq(Opinion::getCategory, ""));
        if (!opinions.isEmpty()) {
            // 构建所有用户ID的集合（去掉重复的ID以及空置（null或者空字符串或者0）），用于批量查询用户信息
            Set<Long> userIds = opinions.stream()
                    .map(Opinion::getMemberId)
                    .filter(Objects::nonNull)
                    .filter(id -> !id.equals(0L))
                    .collect(Collectors.toSet());
            // 批量查询用户角色信息
            List<MemberRoleBo> userRoleMappings = chamberOfCommerceMemberMapper
                    .getUserRolesByUserIds(userIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toSet()));
            // 构建用户id对应角色codes的映射
            Map<String, List<String>> userRoleCodeMap = userRoleMappings.stream()
                    .collect(Collectors.groupingBy(
                            MemberRoleBo::getMemberId,
                            Collectors.mapping(MemberRoleBo::getRoleCode, Collectors.toList())));

            opinions.forEach(opinion -> {
                if (opinion.getMemberId() == null) {
                    return;
                }
                // 如果对应的角色仅包含新联会则设置为新联会，否则设置总商会
                if (userRoleCodeMap.get(String.valueOf(opinion.getMemberId()))
                        .contains(ScoringCategoryEnum.XLHHY.getValue()) &&
                        !userRoleCodeMap.get(String.valueOf(opinion.getMemberId()))
                                .contains(ScoringCategoryEnum.SHCY.getValue())) {
                    opinion.setCategory(ScoringCategoryEnum.XLHHY);
                } else {
                    opinion.setCategory(ScoringCategoryEnum.SHCY);
                }
                // 更新人更新时间
                opinion.setUpdateBy(SecurityUtils.getUserId());
                opinion.setUpdateTime(LocalDateTime.now());

                // 因为意见建议之前没有计入记分范围，所以之前是没有统计分数的，所以需要判断一下有没有，没有的话就插入数据
                if (scoringRecordMapper.selectCount(new LambdaQueryWrapper<ScoringRecord>()
                        .eq(ScoringRecord::getOpinionId, opinion.getId())
                        .eq(ScoringRecord::getMemberId, opinion.getMemberId())
                        .eq(ScoringRecord::getScoringType, ScoreTypeEnum.OPINION)) == 0) {
                    // 插入数据
                    ScoringRecord scoringRecord = new ScoringRecord();
                    scoringRecord.setMemberId(opinion.getMemberId());
                    scoringRecord.setMemberName(opinion.getMemberName());
                    scoringRecord.setDepartment(opinion.getDepartment());
                    scoringRecord.setScoringType(ScoreTypeEnum.OPINION);
                    scoringRecord.setScoringDetail(opinion.getContent());
                    // 当前没有审核，所以只有1分
                    scoringRecord.setScore(1L);
                    scoringRecord.setCreateBy(SecurityUtils.getUserId());
                    scoringRecord.setCreateTime(LocalDateTime.now());
                    scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
                    scoringRecord.setCategory(opinion.getCategory().getValue());
                    scoringRecord.setOpinionId(opinion.getId());
                    scoringRecordMapper.insert(scoringRecord);
                } else {
                    // 2. 更新评分记录表数据
                    UpdateWrapper<ScoringRecord> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("opinion_id", opinion.getId())
                            .eq("member_id", opinion.getMemberId())
                            .eq("scoring_type", ScoreTypeEnum.OPINION);

                    // 构建更新对象
                    ScoringRecord scoringRecord = new ScoringRecord();
                    scoringRecord.setCategory(opinion.getCategory().getValue());
                    scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                    scoringRecord.setUpdateTime(LocalDateTime.now());

                    scoringRecordMapper.update(scoringRecord, updateWrapper);
                }

            });
            // 批量更新表格数据
            this.updateBatchById(opinions);
        }

        return true;
    }
}