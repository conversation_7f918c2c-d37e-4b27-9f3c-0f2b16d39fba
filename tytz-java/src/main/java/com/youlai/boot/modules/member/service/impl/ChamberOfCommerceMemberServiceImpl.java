package com.youlai.boot.modules.member.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.member.model.query.ChamberOfCommerceMemberQuery;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;
import com.youlai.boot.modules.member.service.IChamberOfCommerceMemberService;

import lombok.RequiredArgsConstructor;

/**
 * 商会成员管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@RequiredArgsConstructor
public class ChamberOfCommerceMemberServiceImpl implements IChamberOfCommerceMemberService {
    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    /**
     * 获取商会会员列表（原有方法，保持兼容性）
     *
     * @param queryParams 查询参数
     * @return 商会会员列表
     */
    @Override
    public List<ChamberOfCommerceMemberVO> getChamberOfCommerceMembers(ChamberOfCommerceMemberQuery queryParams) {
        List<ChamberOfCommerceMemberVO> result = chamberOfCommerceMemberMapper
                .getChamberOfCommerceMemberList(queryParams);
        return result;
    }

    /**
     * 高性能获取商会会员列表（包含角色信息）
     * 使用分步查询 + 批量查询避免 N+1 问题
     *
     * @param queryParams 查询参数
     * @return 包含角色信息的商会会员列表
     */
    public List<ChamberOfCommerceMemberVO> getChamberOfCommerceMembersWithRoles(
            ChamberOfCommerceMemberQuery queryParams) {
        // 第一步：查询用户基本信息
        List<ChamberOfCommerceMemberVO> members = chamberOfCommerceMemberMapper
                .getChamberOfCommerceMemberList(queryParams);

        if (members.isEmpty()) {
            return members;
        }

        // 第二步：批量查询所有用户的角色信息
        Set<String> userIds = members.stream()
                .map(ChamberOfCommerceMemberVO::getId)
                .collect(Collectors.toSet());

        List<MemberRoleBo> userRoleMappings = chamberOfCommerceMemberMapper
                .getUserRolesByUserIds(userIds);

        // 第三步：按用户ID分组角色信息
        Map<String, List<ChamberOfCommerceMemberVO.RoleInfo>> userRolesMap = userRoleMappings.stream()
                .collect(Collectors.groupingBy(
                        MemberRoleBo::getMemberId,
                        Collectors.mapping(mapping -> {
                            ChamberOfCommerceMemberVO.RoleInfo roleInfo = new ChamberOfCommerceMemberVO.RoleInfo();
                            roleInfo.setRoleId(mapping.getRoleId());
                            roleInfo.setRoleName(mapping.getRoleName());
                            roleInfo.setRoleCode(mapping.getRoleCode());
                            return roleInfo;
                        }, Collectors.toList())));

        // 第四步：将角色信息设置到用户对象中
        members.forEach(member -> {
            List<ChamberOfCommerceMemberVO.RoleInfo> roles = userRolesMap.get(member.getId());
            member.setRoles(roles != null ? roles : List.of());
            member.setDeptName(member.getRoles().stream()
                    .map(ChamberOfCommerceMemberVO.RoleInfo::getRoleName)
                    .collect(Collectors.joining("、")));
        });

        return members;
    }

}
