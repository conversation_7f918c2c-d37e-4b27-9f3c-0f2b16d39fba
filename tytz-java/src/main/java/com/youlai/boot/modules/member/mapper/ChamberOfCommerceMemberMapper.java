package com.youlai.boot.modules.member.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.member.model.query.ChamberOfCommerceMemberQuery;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;
import com.youlai.boot.system.model.entity.User;

/**
 * 商会成员管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Mapper
public interface ChamberOfCommerceMemberMapper extends BaseMapper<User> {

    /**
     * 获取商会会员列表（不包含角色信息）
     *
     * @param queryParams 查询参数
     * @return 商会会员列表
     */
    List<ChamberOfCommerceMemberVO> getChamberOfCommerceMemberList(
            @Param("queryParams") ChamberOfCommerceMemberQuery queryParams);

    /**
     * 批量获取用户角色信息
     *
     * @param userIds 用户ID集合
     * @return 用户角色映射关系
     */
    List<MemberRoleBo> getUserRolesByUserIds(@Param("userIds") Set<String> userIds);

    /**
     * 高性能查询：使用 ResultMap 一次性获取用户和角色信息
     *
     * @param queryParams 查询参数
     * @return 包含角色信息的商会会员列表
     */
    List<ChamberOfCommerceMemberVO> getChamberOfCommerceMembersWithRolesOptimized(
            @Param("queryParams") ChamberOfCommerceMemberQuery queryParams);
}
