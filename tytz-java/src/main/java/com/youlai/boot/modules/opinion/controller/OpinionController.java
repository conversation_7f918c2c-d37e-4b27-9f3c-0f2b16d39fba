package com.youlai.boot.modules.opinion.controller;

import com.youlai.boot.modules.opinion.service.OpinionService;
import com.youlai.boot.modules.opinion.model.form.OpinionForm;
import com.youlai.boot.modules.opinion.model.query.OpinionQuery;
import com.youlai.boot.modules.opinion.model.vo.OpinionVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import com.alibaba.excel.EasyExcel;
import com.youlai.boot.modules.opinion.model.query.OpinionQuery;
import com.youlai.boot.modules.opinion.model.query.MyOpinionQuery;
import com.youlai.boot.modules.opinion.model.vo.OpinionExcelVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 意见征集前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Tag(name = "意见征集接口")
@RestController
@RequestMapping("/api/v1/opinions")
@RequiredArgsConstructor
public class OpinionController {

    private final OpinionService opinionService;

    @Operation(summary = "意见征集分页列表")
    @GetMapping("/page")
    public PageResult<OpinionVO> getOpinionPage(OpinionQuery queryParams) {
        IPage<OpinionVO> result = opinionService.getOpinionPage(queryParams);
        return PageResult.success(result);
    }

    @GetMapping("/my/page")
    @Operation(summary = "获得我的提交的意见征集")
    public PageResult<OpinionVO> getMyOpinionPage(MyOpinionQuery queryParams) {
        IPage<OpinionVO> result = opinionService.getMyOpinionPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增意见征集")
    @PostMapping
    public Result<Void> saveOpinion(@RequestBody @Valid OpinionForm formData) {
        boolean result = opinionService.saveOpinion(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取意见征集表单数据")
    @GetMapping("/{id}/form")
    public Result<OpinionForm> getOpinionForm(
            @Parameter(description = "意见征集ID") @PathVariable Long id) {
        OpinionForm formData = opinionService.getOpinionFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改意见征集")
    @PutMapping(value = "/{id}")
    public Result<Void> updateOpinion(
            @Parameter(description = "意见征集ID") @PathVariable Long id,
            @RequestBody @Validated OpinionForm formData) {
        boolean result = opinionService.updateOpinion(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除意见征集")
    @DeleteMapping("/{ids}")
    public Result<Void> deleteOpinions(
            @Parameter(description = "意见征集ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = opinionService.deleteOpinions(ids);
        return Result.judge(result);
    }

    /**
     * 导出意见建议数据
     */
    @Operation(summary = "导出意见建议数据")
    @PostMapping("/export")
    public void exportOpinions(HttpServletResponse response, @RequestBody OpinionQuery queryParams) throws IOException {
        // 设置响应头
        String fileName = "意见建议数据.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        // 获取数据
        List<OpinionVO> list = opinionService.getExportList(queryParams);
        List<OpinionExcelVO> excelVOList = list.stream()
                .map(item -> {
                    OpinionExcelVO excelVO = new OpinionExcelVO();
                    BeanUtils.copyProperties(item, excelVO);
                    excelVO.setIndex(Long.valueOf(list.indexOf(item) + 1));
                    return excelVO;
                }).collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), OpinionExcelVO.class)
                .sheet("意见建议数据")
                .doWrite(excelVOList);
    }

    /**
     * 重新统计分数
     * TODO: 发版更新后需要删掉的
     */
    @Operation(summary = "重新统计分数")
    @GetMapping("/re-calculate-score")
    public Result<Boolean> reCalculateScore() {
        boolean result = opinionService.reCalculateScore();
        return Result.judge(result);
    }
}