package com.youlai.boot.modules.problem.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.modules.problem.model.dto.ProblemAdoptDTO;
import com.youlai.boot.modules.problem.model.dto.ProblemInstructDTO;
import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.problem.model.form.ProblemForm;
import com.youlai.boot.modules.problem.model.query.ProblemQuery;
import com.youlai.boot.modules.problem.model.query.MyProblemQuery;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import com.youlai.boot.modules.scoringrecord.service.ScoringRecordService;
import com.youlai.boot.modules.scoringrecord.service.impl.ScoringRecordServiceImpl;

import java.util.List;

/**
 * 营商环境问题服务接口
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface ProblemService extends IService<Problem> {

    /**
     * 获取营商环境问题分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<ProblemVO> getProblemPage(ProblemQuery queryParams);

    /**
     * 获取营商环境问题表单数据
     *
     * @param id 问题ID
     * @return 表单数据
     */
    ProblemForm getProblemFormData(Long id);

    /**
     * 获取我提交的问题分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<ProblemVO> getMyProblemPage(MyProblemQuery queryParams);

    /**
     * 新增营商环境问题
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveProblem(ProblemForm formData);

    /**
     * 修改营商环境问题
     *
     * @param id       问题ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateProblem(Long id, ProblemForm formData);

    /**
     * 删除营商环境问题
     *
     * @param ids 问题ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteProblems(String ids);

    /**
     * 采纳问题
     *
     * @param dto 入参对象
     * @return 是否成功
     */
    boolean adoptProblem(ProblemAdoptDTO dto);

    /**
     * 批示问题
     *
     * @param dto 入参对象
     * @return 是否成功
     */
    boolean instructProblem(ProblemInstructDTO dto);

    /**
     * 获取领导批示分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<ProblemVO> getLeaderInstructPage(ProblemQuery queryParams);

    /**
     * 获取导出数据列表
     *
     * @param queryParams 查询参数
     * @return 导出数据列表
     */
    List<ProblemVO> getExportList(ProblemQuery queryParams);

    /**
     * 更新旧数据
     */
    boolean updateOldData();
}