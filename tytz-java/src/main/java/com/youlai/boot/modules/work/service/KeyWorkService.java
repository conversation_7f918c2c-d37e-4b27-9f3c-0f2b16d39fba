package com.youlai.boot.modules.work.service;

import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import com.youlai.boot.modules.work.model.query.KeyWorkQuery;
import com.youlai.boot.modules.work.model.vo.KeyWorkVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.system.model.bo.UserBO;

import java.util.List;

/**
 * 年度重点工作服务类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface KeyWorkService extends IService<KeyWork> {

    /**
     * 年度重点工作分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<KeyWorkVO> getKeyWorkPage(KeyWorkQuery queryParams);

    /**
     * 获取年度重点工作表单数据
     *
     * @param id 重点工作ID
     * @return 表单数据
     */
    KeyWorkForm getKeyWorkFormData(Long id);

    /**
     * 新增年度重点工作
     *
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    boolean saveKeyWork(KeyWorkForm formData);

    /**
     * 修改年度重点工作
     *
     * @param id       重点工作ID
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    boolean updateKeyWork(Long id, KeyWorkForm formData);

    /**
     * 删除年度重点工作
     *
     * @param ids 重点工作ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteKeyWorks(String ids);

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    List<UserBO> getUserBOListByIds(List<Long> userIds);

    /**
     * 更新旧数据
     * 
     * @return 是否成功
     */
    boolean updateOldData();

}