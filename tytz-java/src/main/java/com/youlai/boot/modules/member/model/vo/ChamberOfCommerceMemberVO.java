package com.youlai.boot.modules.member.model.vo;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "商会会员列表对象")
@Data
public class ChamberOfCommerceMemberVO {
    @Schema(description = "会员ID")
    private String id;

    @Schema(description = "会员名称")
    private String username;

    @Schema(description = "会员昵称")
    private String nickname;

    @Schema(description = "会员手机号")
    private String mobile;

    @Schema(description = "会员性别")
    private Integer gender;

    @Schema(description = "会员用户头像地址")
    private String avatar;

    @Schema(description = "会员邮箱")
    private String email;

    @Schema(description = "会员用户状态(1:启用;0:禁用)")
    private Integer status;

    // 这里实际用的角色名称（总商会和新联会是通过分配角色实现的）
    // NOTE: 20250626不要这个属性了
    @Schema(description = "会员所属部门名称，多个部门名称使用英文逗号(,)分割")
    private String deptName;

    @Schema(description = "职务")
    private String company;

    @Schema(description = "用户角色列表")
    private List<RoleInfo> roles;

    /**
     * 角色信息内部类
     */
    @Schema(description = "角色信息")
    @Data
    public static class RoleInfo {
        @Schema(description = "角色ID")
        private String roleId;

        @Schema(description = "角色名称")
        private String roleName;

        @Schema(description = "角色编码")
        private String roleCode;
    }
}
