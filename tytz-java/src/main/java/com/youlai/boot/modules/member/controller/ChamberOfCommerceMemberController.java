package com.youlai.boot.modules.member.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.member.model.query.ChamberOfCommerceMemberQuery;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;
import com.youlai.boot.modules.member.service.IChamberOfCommerceMemberService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 商会会员管理控制层
 * 
 * NOTE: 20250626 当前实现中是通过角色分配实现的 [需求：”将用户划分为总商会和新联会“ ]
 * NOTE: 新增对应的评分信息时需要获取对应的用户列表，涉及到业务的内容，所以不在系统对应的模块去做
 * 
 * <AUTHOR>
 * @since 2025-06-26
 */
@Tag(name = "商会会员管理接口")
@RestController
// coc: chamber-of-commerce
@RequestMapping("/api/v1/coc-members")
@RequiredArgsConstructor
public class ChamberOfCommerceMemberController {

    private final IChamberOfCommerceMemberService chamberOfCommerceMemberService;

    /**
     * 获取商会会员列表（包含总商会和新联会）
     */
    @Operation(summary = "获取商会会员列表")
    @PostMapping("/list")
    public Result<List<ChamberOfCommerceMemberVO>> getChamberOfCommerceMemberList(
            ChamberOfCommerceMemberQuery queryParams) {
        List<ChamberOfCommerceMemberVO> list = chamberOfCommerceMemberService
                // .getChamberOfCommerceMembers(queryParams);
                .getChamberOfCommerceMembersWithRoles(queryParams);
        return Result.success(list);
    }
}
