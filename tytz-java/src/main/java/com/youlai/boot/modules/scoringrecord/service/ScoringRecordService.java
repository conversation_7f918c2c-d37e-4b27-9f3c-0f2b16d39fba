package com.youlai.boot.modules.scoringrecord.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.modules.scoringrecord.model.dto.ScoringRecordDTO;
import com.youlai.boot.modules.scoringrecord.model.form.ScoringRecordForm;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordDetailQuery;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordPageQuery;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordDetailVO;

import java.util.List;

/**
 * 评分记录服务接口
 */
public interface ScoringRecordService extends IService<ScoringRecord> {

    /**
     * 获取评分记录分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<ScoringRecordVO> getScoringRecordPage(ScoringRecordPageQuery queryParams);

    /**
     * 获取评分记录详情
     *
     * @param id 评分记录ID
     * @return 评分记录详情
     */
    ScoringRecordDTO getScoringRecordDetail(Long id);

    /**
     * 新增评分记录
     *
     * @param form 表单对象
     * @return 是否成功
     */
    boolean saveScoringRecord(ScoringRecordForm form);

    /**
     * 修改评分记录
     *
     * @param id   评分记录ID
     * @param form 表单对象
     * @return 是否成功
     */
    boolean updateScoringRecord(Long id, ScoringRecordForm form);

    /**
     * 删除评分记录
     *
     * @param ids 评分记录ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteScoringRecords(String ids);

    /**
     * 根据查询条件分页获取评分记录详情
     * 支持按年度、会员ID和评分类型进行筛选
     * 详细信息包含当前活动的活动名称，选择的活动类型、当前活动获得的评分
     *
     * @param queryParams 查询参数
     * @return 分页评分记录详情
     */
    Page<ScoringRecordDetailVO> getScoringRecordDetailPage(ScoringRecordDetailQuery queryParams);

    /**
     * 获取评分记录导出数据
     * 支持根据逗号分隔的ID导出、根据查询条件导出或导出全部记录
     *
     * @param queryParams 查询参数，包含导出条件
     * @return 导出数据列表
     */
    List<ScoringRecordVO> getExportList(ScoringRecordPageQuery queryParams);

}