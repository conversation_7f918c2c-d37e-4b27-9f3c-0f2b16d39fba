package com.youlai.boot.modules.work.service.impl;

import cn.hutool.json.JSONUtil;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.common.enums.WorkTypeEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.meeting.converter.MeetingConverter;
import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.work.mapper.KeyWorkMapper;
import com.youlai.boot.modules.work.service.KeyWorkService;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import com.youlai.boot.modules.work.model.query.KeyWorkQuery;
import com.youlai.boot.modules.work.model.vo.KeyWorkVO;
import com.youlai.boot.modules.work.converter.KeyWorkConverter;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 年度重点工作服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
@RequiredArgsConstructor
public class KeyWorkServiceImpl extends ServiceImpl<KeyWorkMapper, KeyWork> implements KeyWorkService {

    private final KeyWorkConverter keyWorkConverter;
    private final UserService userService;
    private final ScoringRecordMapper scoringRecordMapper;
    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 获取年度重点工作分页列表
     *
     * @param queryParams 查询参数
     * @return 重点工作分页列表
     */
    @Override
    public IPage<KeyWorkVO> getKeyWorkPage(KeyWorkQuery queryParams) {
        Page<KeyWorkVO> pageVO = this.baseMapper.getKeyWorkPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams);
        return pageVO;
    }

    /**
     * 获取年度重点工作表单数据
     *
     * @param id 重点工作ID
     * @return 表单数据
     */
    @Override
    public KeyWorkForm getKeyWorkFormData(Long id) {
        KeyWork entity = this.getById(id);
        Assert.notNull(entity, "重点工作不存在");
        // 解析参与人员ID数组
        List<Map> participantMaps = JSONUtil.toList(entity.getParticipants(), Map.class);
        List<Long> participantIds = participantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 查询用户信息，使用UserBO对象
        List<UserBO> userBOs = getUserBOListByIds(participantIds);

        // 转换为参与者ID DTO
        List<KeyWorkForm.ParticipantDTO> participantDTOs = userBOs.stream()
                .map(userBO -> {
                    return KeyWorkForm.ParticipantDTO.builder()
                            .id(userBO.getId())
                            .businessName(userBO.getNickname())
                            .businessMember(userBO.getCompany() + " - " + userBO.getDeptName())
                            .build();
                })
                .collect(Collectors.toList());

        List<KeyWorkForm.FileDTO> attachments = JSONUtil.toList(entity.getAttachments(), KeyWorkForm.FileDTO.class);

        KeyWorkForm form = keyWorkConverter.toForm(entity);
        form.setParticipants(participantDTOs);
        form.setAttachments(attachments);
        return form;
    }

    /**
     * 新增年度重点工作
     *
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveKeyWork(KeyWorkForm formData) {
        KeyWork entity = keyWorkConverter.toEntity(formData);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());
        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());
        entity.setCreateTime(LocalDateTime.now());

        // 先保存工作信息，获取工作ID
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 使用保存后的工作ID创建评分记录
        Long workId = entity.getId();

        // 为每个参与者创建评分记录
        formData.getParticipants().stream().forEach(participant -> {
            ScoringRecord scoringRecord = new ScoringRecord();
            scoringRecord.setMemberId(participant.getId());
            scoringRecord.setWorkId(workId); // 设置工作ID

            // 根据用户ID查询用户信息
            UserBO userBO = userMapper.getUserProfile(participant.getId());
            if (userBO != null) {
                // 设置会员名称和部门
                scoringRecord.setMemberName(userBO.getNickname());
                scoringRecord.setDepartment(userBO.getCompany());
            } else {
                // 如果查询不到用户信息，则使用参与者对象中的信息
                scoringRecord.setMemberName(participant.getBusinessName());
                scoringRecord.setDepartment(participant.getBusinessMember());
            }

            scoringRecord.setScoringType(ScoreTypeEnum.KEY_WORK);
            scoringRecord.setScoringDetail(formData.getWorkType().getLabel());

            // 根据工作类型设置分数
            if (WorkTypeEnum.PROJECT_SERVICE.equals(formData.getWorkType())) {
                // 助力项目建设服务，参加引进外资活动得3分
                scoringRecord.setScore(3L);
            } else if (WorkTypeEnum.BUSINESS_ENVIRONMENT.equals(formData.getWorkType())) {
                // 助推创一流营商环境得3分
                scoringRecord.setScore(3L);
            } else if (WorkTypeEnum.OTHER_TASKS.equals(formData.getWorkType())) {
                // 完成区商会交办的其他任务得3分
                scoringRecord.setScore(3L);
            }

            scoringRecord.setCreateBy(SecurityUtils.getUserId());
            scoringRecord.setCreateTime(LocalDateTime.now());
            scoringRecord.setYear(String.valueOf(formData.getYear()));

            // TODO: 这里实际上这样做会有问题，因为同时属于总商会和新联会的归类，用户可能仅仅在一个部门里面导致数据不正确，其他涉及归类的同样有这个问题
            formData.getCategory().stream().forEach(category -> {
                scoringRecord.setCategory(category.getValue());
                // 复制对象，避免id相同
                scoringRecord.setId(null);
                scoringRecordMapper.insert(scoringRecord);
            });
            // scoringRecordMapper.insert(scoringRecord);
        });

        return true;
    }

    /**
     * 更新年度重点工作
     *
     * @param id       重点工作ID
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateKeyWork(Long id, KeyWorkForm formData) {
        // 获取原工作信息
        KeyWork oldWork = this.getById(id);
        Assert.notNull(oldWork, "年度重点工作不存在");

        KeyWork entity = keyWorkConverter.toEntity(formData);
        entity.setId(id);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());
        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 解析原参与人员ID数组
        List<Map> oldParticipantMaps = JSONUtil.toList(oldWork.getParticipants(), Map.class);
        List<Long> oldParticipantIds = oldParticipantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 解析新参与人员ID数组
        List<Long> newParticipantIds = formData.getParticipants().stream()
                .map(participant -> participant.getId())
                .collect(Collectors.toList());

        // 如果工作类型发生变化或者参与人员发生变化，需要更新评分记录
        boolean workTypeChanged = !oldWork.getWorkType().equals(entity.getWorkType());
        boolean participantsChanged = !oldParticipantIds.equals(newParticipantIds);

        if (workTypeChanged || participantsChanged) {
            // 删除原有的评分记录
            scoringRecordMapper.deleteByWorkId(id);

            // 重新创建评分记录
            formData.getParticipants().stream().forEach(participant -> {
                ScoringRecord scoringRecord = new ScoringRecord();
                scoringRecord.setMemberId(participant.getId());
                scoringRecord.setWorkId(id); // 设置工作ID

                // 根据用户ID查询用户信息
                UserBO userBO = userMapper.getUserProfile(participant.getId());
                if (userBO != null) {
                    // 设置会员名称和部门
                    scoringRecord.setMemberName(userBO.getNickname());
                    scoringRecord.setDepartment(userBO.getCompany());
                } else {
                    // 如果查询不到用户信息，则使用参与者对象中的信息
                    scoringRecord.setMemberName(participant.getBusinessName());
                    scoringRecord.setDepartment(participant.getBusinessMember());
                }

                scoringRecord.setScoringType(ScoreTypeEnum.KEY_WORK);
                scoringRecord.setScoringDetail(formData.getWorkType().getLabel());

                // 根据工作类型设置分数
                if (WorkTypeEnum.PROJECT_SERVICE.equals(formData.getWorkType())) {
                    // 助力项目建设服务，参加引进外资活动得5分
                    scoringRecord.setScore(3L);
                } else if (WorkTypeEnum.BUSINESS_ENVIRONMENT.equals(formData.getWorkType())) {
                    // 助推创一流营商环境得3分
                    scoringRecord.setScore(3L);
                } else if (WorkTypeEnum.OTHER_TASKS.equals(formData.getWorkType())) {
                    // 完成区商会交办的其他任务得2分
                    scoringRecord.setScore(3L);
                }

                scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                scoringRecord.setUpdateTime(LocalDateTime.now());
                scoringRecord.setYear(String.valueOf(formData.getYear()));
                formData.getCategory().stream().forEach(category -> {
                    scoringRecord.setCategory(category.getValue());
                    // 复制对象，避免id相同
                    scoringRecord.setId(null);
                    scoringRecordMapper.insert(scoringRecord);
                });
                // scoringRecordMapper.insert(scoringRecord);
            });
        }

        return this.updateById(entity);
    }

    /**
     * 删除年度重点工作
     *
     * @param ids 重点工作ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteKeyWorks(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的重点工作数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();

        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByWorkId(id);
        }

        return this.removeByIds(idList);
    }

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    @Override
    public List<UserBO> getUserBOListByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 直接使用UserService的listByIds方法获取User实体
        List<User> users = userService.listByIds(userIds);

        // 使用UserMapper获取用户信息，包含部门名称等
        List<UserBO> userBOs = new ArrayList<>();
        for (User user : users) {
            // 获取用户个人信息，包含部门名称
            UserBO userBO = userMapper.getUserProfile(user.getId());
            if (userBO != null) {
                userBOs.add(userBO);
            }
        }

        return userBOs;
    }

    @Override
    public boolean updateOldData() {
        // 1. 更新重点工作表数据
        // 获取所有category为null或者空字符串的的重点工作表数据
        List<KeyWork> keyWorks = this.list(new LambdaQueryWrapper<KeyWork>()
                .isNull(KeyWork::getCategory)
                .or()
                .eq(KeyWork::getCategory, ""));
        if (!keyWorks.isEmpty()) {
            // 构建所有用户ID的集合（去掉重复的ID以及空置（null或者空字符串或者0）），用于批量查询用户信息
            Set<Long> userIds = keyWorks.stream()
                    .map(KeyWork::getParticipants)
                    .filter(Objects::nonNull)
                    .filter(participants -> !participants.isEmpty())
                    .flatMap(participants -> JSONUtil.toList(participants, Map.class).stream())
                    .map(map -> Long.parseLong(map.get("id").toString()))
                    .collect(Collectors.toSet());
            // 批量查询用户角色信息
            List<MemberRoleBo> userRoleMappings = chamberOfCommerceMemberMapper
                    .getUserRolesByUserIds(userIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toSet()));
            // 构建用户id对应角色codes的映射
            Map<String, List<String>> userRoleCodeMap = userRoleMappings.stream()
                    .collect(Collectors.groupingBy(
                            MemberRoleBo::getMemberId,
                            Collectors.mapping(MemberRoleBo::getRoleCode, Collectors.toList())));

            keyWorks.forEach(keyWork -> {
                // 设置重点工作分类
                List<String> participantIds = JSONUtil.toList(keyWork.getParticipants(), Map.class)
                        .stream()
                        .map(map -> map.get("id").toString())
                        .collect(Collectors.toList());
                // 是否存在任意一个参与用户的角色包含总商会且不包含新联会
                boolean isAnySHCY = false;
                // 是否存在任意一个参与用户的角色包含新联会且不包含总商会
                boolean isAnyXLHHY = false;
                // 是否存在任意一个参与用户同时包含总商会和新联会
                boolean isAnyBoth = false;
                // 设置活动分类
                for (String participantId : participantIds) {
                    List<String> roleCodes = userRoleCodeMap.get(participantId);
                    if (roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())
                            && !roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())) {
                        isAnySHCY = true;
                    }
                    if (roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())
                            && !roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())) {
                        isAnyXLHHY = true;
                    }
                    if (roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())
                            && roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())) {
                        isAnyBoth = true;
                    }
                }
                // 活动归类列表
                List<ScoringCategoryEnum> categoryList = new ArrayList<>();
                if (isAnySHCY && isAnyXLHHY) {
                    categoryList.add(ScoringCategoryEnum.SHCY);
                    categoryList.add(ScoringCategoryEnum.XLHHY);
                } else if (isAnyBoth) {
                    if (isAnySHCY) {
                        categoryList.add(ScoringCategoryEnum.SHCY);
                    }
                    if (isAnyXLHHY) {
                        categoryList.add(ScoringCategoryEnum.XLHHY);
                    }
                } else if (!isAnySHCY) {
                    categoryList.add(ScoringCategoryEnum.XLHHY);
                } else if (!isAnyXLHHY) {
                    categoryList.add(ScoringCategoryEnum.SHCY);
                }
                keyWork.setCategory(MeetingConverter.categoryToString(categoryList));

                // 更新人更新时间
                keyWork.setUpdateBy(SecurityUtils.getUserId());
                keyWork.setUpdateTime(LocalDateTime.now());

                // 更新评分记录表数据
                for (String participantId : participantIds) {
                    // 归属多个部门需要拆分成多条记录
                    categoryList.forEach(category -> {
                        UpdateWrapper<ScoringRecord> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("work_id", keyWork.getId())
                                .eq("scoring_type", ScoreTypeEnum.KEY_WORK)
                                .eq("member_id", participantId);
                        ScoringRecord scoringRecord = new ScoringRecord();
                        scoringRecord.setCategory(category.getValue());
                        scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                        scoringRecord.setUpdateTime(LocalDateTime.now());
                        scoringRecordMapper.update(scoringRecord, updateWrapper);
                    });
                }
            });
            // 批量更新表格数据
            this.updateBatchById(keyWorks);
        }
        return true;
    }
}