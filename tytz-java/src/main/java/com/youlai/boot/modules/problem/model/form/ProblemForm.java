package com.youlai.boot.modules.problem.model.form;

import java.io.Serial;
import java.io.Serializable;

import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import jakarta.validation.constraints.*;

/**
 * 营商环境问题表单对象
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@Schema(description = "营商环境问题表单对象")
public class ProblemForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "营商环境标题")
    @NotBlank(message = "标题不能为空")
    @Size(max = 255, message = "标题长度不能超过255个字符")
    private String title;

    @Schema(description = "营商环境问题内容")
    @NotBlank(message = "问题内容不能为空")
    private String content;

    @Schema(description = "营商环境类别")
    @NotBlank(message = "环境类别不能为空")
    @Size(max = 50, message = "环境类别长度不能超过50个字符")
    private String businessType;

    @Schema(description = "问题归属")
    @NotNull(message = "问题归属不能为空")
    private ScoringCategoryEnum category;

    @Schema(description = "商会会员id")
    private String memberId;

    @Schema(description = "商会会员名称")
    private String memberName;

    @Schema(description = "所属部门")
    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    private String department;

    @Schema(description = "采纳状态")
    private AdoptStatusEnum adoptStatus;

    @Schema(description = "采纳意见")
    private String adoptContent;

    @Schema(description = "采纳人")
    private String adoptBy;

    @Schema(description = "批示状态")
    private InstructionStatusEnum instructionStatus;

    @Schema(description = "批示内容")
    private String instructionContent;

    @Schema(description = "批示人")
    private String instructionBy;

    @Schema(description = "领导批示")
    private String leaderInstruction;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "采纳时间")
    private LocalDateTime adoptTime;

    @Schema(description = "用户身份")
    private String memberRole;

}