package com.youlai.boot.modules.activity.model.form;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.youlai.boot.common.enums.ActivityTypeEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;

import jakarta.validation.constraints.*;

/**
 * 活动管理表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema(description = "活动管理表单对象")
public class ActivityForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 100, message = "活动名称长度不能超过100个字符")
    private String title;

    @Schema(description = "活动类型")
    @NotNull(message = "活动类型不能为空")
    private ActivityTypeEnum activityType;

    @Schema(description = "活动归类")
    @NotNull(message = "活动归类不能为空")
    private List<ScoringCategoryEnum> category;

    // @Schema(description = "主办单位")
    // @NotBlank(message = "主办单位不能为空")
    // @Size(max=50, message="主办单位长度不能超过50个字符")
    // private String department;

    @Schema(description = "参与人员ID列表")
    private List<ParticipantDTO> participants;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "活动内容")
    private String content;

    @Schema(description = "附件")
    private List<FileDTO> attachments;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ParticipantDTO {
        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户名")
        private String businessName;

        @Schema(description = "用户信息")
        private String businessMember;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {
        @Schema(description = "文件url")
        String url;

        @Schema(description = "文件名称")
        String name;
    }

}