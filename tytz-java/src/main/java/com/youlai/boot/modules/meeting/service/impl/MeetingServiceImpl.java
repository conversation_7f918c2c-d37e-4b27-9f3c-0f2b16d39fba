package com.youlai.boot.modules.meeting.service.impl;

import cn.hutool.json.JSONUtil;
import com.youlai.boot.common.enums.MeetingTypeEnum;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.service.UserService;
import com.youlai.boot.system.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.meeting.mapper.MeetingMapper;
import com.youlai.boot.modules.meeting.service.MeetingService;
import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.bo.MemberRoleBo;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.meeting.model.form.MeetingForm;
import com.youlai.boot.modules.meeting.model.query.MeetingQuery;
import com.youlai.boot.modules.meeting.model.vo.MeetingVO;
import com.youlai.boot.modules.meeting.converter.MeetingConverter;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会议管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Service
@RequiredArgsConstructor
public class MeetingServiceImpl extends ServiceImpl<MeetingMapper, Meeting> implements MeetingService {

    private final MeetingConverter meetingConverter;

    private final ScoringRecordMapper scoringRecordMapper;

    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    private final UserService userService;
    @Autowired
    private UserMapper userMapper;

    /**
     * 获取会议管理分页列表
     *
     * @param queryParams 查询参数
     * @return 会议分页列表
     */
    @Override
    public IPage<MeetingVO> getMeetingPage(MeetingQuery queryParams) {
        Page<MeetingVO> pageVO = this.baseMapper.getMeetingPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams);
        return pageVO;
    }

    /**
     * 获取会议管理表单数据
     *
     * @param id 会议ID
     * @return 表单数据
     */
    @Override
    public MeetingForm getMeetingFormData(Long id) {
        Meeting entity = this.getById(id);
        Assert.notNull(entity, "会议不存在");
        // 解析参与人员ID数组
        List<Map> participantMaps = JSONUtil.toList(entity.getParticipants(), Map.class);
        List<Long> participantIds = participantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 查询用户信息，使用UserBO对象
        List<UserBO> userBOs = getUserBOListByIds(participantIds);

        // 转换为参与者ID DTO
        List<MeetingForm.ParticipantDTO> participantDTOs = userBOs.stream()
                .map(userBO -> {
                    return MeetingForm.ParticipantDTO.builder()
                            .id(userBO.getId())
                            .businessName(userBO.getNickname())
                            .businessMember(userBO.getCompany() + " - " + userBO.getDeptName())
                            .build();
                })
                .collect(Collectors.toList());

        List<MeetingForm.FileDTO> attachments = JSONUtil.toList(entity.getAttachments(), MeetingForm.FileDTO.class);

        MeetingForm form = meetingConverter.toForm(entity);
        form.setParticipants(participantDTOs);
        form.setAttachments(attachments);
        return form;
    }

    /**
     * 新增会议
     *
     * @param formData 会议表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveMeeting(MeetingForm formData) {
        Meeting entity = meetingConverter.toEntity(formData);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());
        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());

        // 先保存会议信息，获取会议ID
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 使用保存后的会议ID创建评分记录
        Long meetingId = entity.getId();

        formData.getParticipants().stream().forEach(participant -> {
            ScoringRecord scoringRecord = new ScoringRecord();
            scoringRecord.setMemberId(participant.getId());
            scoringRecord.setMeetingId(meetingId); // 设置会议ID

            // 根据用户ID查询用户信息
            UserBO userBO = userMapper.getUserProfile(participant.getId());
            if (userBO != null) {
                // 设置会员名称和部门
                scoringRecord.setMemberName(userBO.getNickname());
                scoringRecord.setDepartment(userBO.getCompany());
            } else {
                // 如果查询不到用户信息，则使用参与者对象中的信息
                scoringRecord.setMemberName(participant.getBusinessMember());
                scoringRecord.setDepartment(participant.getBusinessName());
            }
            scoringRecord.setScoringType(ScoreTypeEnum.MEETING);
            scoringRecord.setScoringDetail(formData.getMeetingType().getLabel());
            if (MeetingTypeEnum.MEMBER.equals(formData.getMeetingType())) {
                // 参加会员（代表）大会得5分
                scoringRecord.setScore(5L);
            }
            if (MeetingTypeEnum.PRESIDENT.equals(formData.getMeetingType())) {
                // 参加会长会议得2分
                scoringRecord.setScore(2L);
            }
            if (MeetingTypeEnum.DIRECTOR.equals(formData.getMeetingType())) {
                // 参加理事会会议得2分
                scoringRecord.setScore(2L);
            }
            scoringRecord.setCreateBy(SecurityUtils.getUserId());
            scoringRecord.setCreateTime(LocalDateTime.now());
            scoringRecord.setYear(String.valueOf(formData.getStartTime().getYear()));

            formData.getCategory().stream().forEach(category -> {
                scoringRecord.setCategory(category.getValue());
                // 复制对象，避免id相同
                scoringRecord.setId(null);
                scoringRecordMapper.insert(scoringRecord);
            });
            // scoringRecordMapper.insert(scoringRecord);
        });

        return true; // 已经在前面保存了会议信息
    }

    /**
     * 更新会议
     *
     * @param id       会议ID
     * @param formData 会议表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateMeeting(Long id, MeetingForm formData) {
        // 获取原会议信息
        Meeting oldMeeting = this.getById(id);
        Assert.notNull(oldMeeting, "会议不存在");

        Meeting entity = meetingConverter.toEntity(formData);
        entity.setId(id);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());

        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 解析原参与人员ID数组
        List<Map> oldParticipantMaps = JSONUtil.toList(oldMeeting.getParticipants(), Map.class);
        List<Long> oldParticipantIds = oldParticipantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 解析新参与人员ID数组
        List<Long> newParticipantIds = formData.getParticipants().stream()
                .map(participant -> participant.getId())
                .collect(Collectors.toList());

        // 如果会议类型发生变化或者参与人员发生变化，需要更新评分记录
        boolean meetingTypeChanged = !oldMeeting.getMeetingType().equals(entity.getMeetingType());
        boolean participantsChanged = !oldParticipantIds.equals(newParticipantIds);

        if (meetingTypeChanged || participantsChanged) {
            // 删除原有的评分记录
            scoringRecordMapper.deleteByMeetingId(id);

            // 重新创建评分记录
            formData.getParticipants().stream().forEach(participant -> {
                ScoringRecord scoringRecord = new ScoringRecord();
                scoringRecord.setMemberId(participant.getId());
                scoringRecord.setMeetingId(id); // 设置会议ID

                // 根据用户ID查询用户信息
                UserBO userBO = userMapper.getUserProfile(participant.getId());
                if (userBO != null) {
                    // 设置会员名称和部门
                    scoringRecord.setMemberName(userBO.getNickname());
                    scoringRecord.setDepartment(userBO.getCompany());
                } else {
                    // 如果查询不到用户信息，则使用参与者对象中的信息
                    scoringRecord.setMemberName(participant.getBusinessName());
                    scoringRecord.setDepartment(participant.getBusinessMember());
                }

                scoringRecord.setScoringType(ScoreTypeEnum.MEETING);
                scoringRecord.setScoringDetail(formData.getMeetingType().getLabel());

                // 根据会议类型设置分数
                if (MeetingTypeEnum.MEMBER.equals(formData.getMeetingType())) {
                    // 参加会员（代表）大会得5分
                    scoringRecord.setScore(5L);
                } else if (MeetingTypeEnum.PRESIDENT.equals(formData.getMeetingType())) {
                    // 参加会长会议得2分
                    scoringRecord.setScore(2L);
                } else if (MeetingTypeEnum.DIRECTOR.equals(formData.getMeetingType())) {
                    // 参加理事会会议得2分
                    scoringRecord.setScore(2L);
                }
                scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                scoringRecord.setUpdateTime(LocalDateTime.now());
                scoringRecord.setYear(String.valueOf(formData.getStartTime().getYear()));

                formData.getCategory().stream().forEach(category -> {
                    scoringRecord.setCategory(category.getValue());
                    // 复制对象，避免id相同
                    scoringRecord.setId(null);
                    scoringRecordMapper.insert(scoringRecord);
                });
                // scoringRecordMapper.insert(scoringRecord);
            });
        }

        return this.updateById(entity);
    }

    /**
     * 删除会议
     *
     * @param ids 会议ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    public boolean deleteMeetings(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的会议数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();

        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByMeetingId(id);
        }

        return this.removeByIds(idList);
    }

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    @Override
    public List<UserBO> getUserBOListByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 直接使用UserService的listByIds方法获取User实体
        List<User> users = userService.listByIds(userIds);

        // 使用UserMapper获取用户信息，包含部门名称等
        List<UserBO> userBOs = new ArrayList<>();
        for (User user : users) {
            // 获取用户个人信息，包含部门名称
            UserBO userBO = userMapper.getUserProfile(user.getId());
            if (userBO != null) {
                userBOs.add(userBO);
            }
        }

        return userBOs;
    }

    /**
     * 更新旧数据
     * 
     * @return 是否成功
     */
    @Override
    public boolean updateOldData() {
        // 1. 更新会议表数据
        // 获取所有category为null或者空字符串的的会议表数据
        List<Meeting> meetings = this.list(new LambdaQueryWrapper<Meeting>()
                .isNull(Meeting::getCategory)
                .or()
                .eq(Meeting::getCategory, ""));
        if (!meetings.isEmpty()) {
            // 构建所有用户ID的集合（去掉重复的ID以及空置（null或者空字符串或者0）），用于批量查询用户信息
            Set<Long> userIds = meetings.stream()
                    .map(Meeting::getParticipants)
                    .filter(Objects::nonNull)
                    .filter(participants -> !participants.isEmpty())
                    .flatMap(participants -> JSONUtil.toList(participants, Map.class).stream())
                    .map(map -> Long.parseLong(map.get("id").toString()))
                    .collect(Collectors.toSet());
            // 批量查询用户角色信息
            List<MemberRoleBo> userRoleMappings = chamberOfCommerceMemberMapper
                    .getUserRolesByUserIds(userIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toSet()));
            // 构建用户id对应角色codes的映射
            Map<String, List<String>> userRoleCodeMap = userRoleMappings.stream()
                    .collect(Collectors.groupingBy(
                            MemberRoleBo::getMemberId,
                            Collectors.mapping(MemberRoleBo::getRoleCode, Collectors.toList())));

            meetings.forEach(meeting -> {
                // 设置活动分类
                List<String> participantIds = JSONUtil.toList(meeting.getParticipants(), Map.class)
                        .stream()
                        .map(map -> map.get("id").toString())
                        .collect(Collectors.toList());
                // 是否存在任意一个参与用户的角色包含总商会且不包含新联会
                boolean isAnySHCY = false;
                // 是否存在任意一个参与用户的角色包含新联会且不包含总商会
                boolean isAnyXLHHY = false;
                // 是否存在任意一个参与用户同时包含总商会和新联会
                boolean isAnyBoth = false;
                // 设置活动分类
                for (String participantId : participantIds) {
                    List<String> roleCodes = userRoleCodeMap.get(participantId);
                    if (roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())
                            && !roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())) {
                        isAnySHCY = true;
                    }
                    if (roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())
                            && !roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())) {
                        isAnyXLHHY = true;
                    }
                    if (roleCodes.contains(ScoringCategoryEnum.SHCY.getValue())
                            && roleCodes.contains(ScoringCategoryEnum.XLHHY.getValue())) {
                        isAnyBoth = true;
                    }
                }
                // 活动归类列表
                List<ScoringCategoryEnum> categoryList = new ArrayList<>();
                if (isAnySHCY && isAnyXLHHY) {
                    categoryList.add(ScoringCategoryEnum.SHCY);
                    categoryList.add(ScoringCategoryEnum.XLHHY);
                } else if (isAnyBoth) {
                    if (isAnySHCY) {
                        categoryList.add(ScoringCategoryEnum.SHCY);
                    }
                    if (isAnyXLHHY) {
                        categoryList.add(ScoringCategoryEnum.XLHHY);
                    }
                } else if (!isAnySHCY) {
                    categoryList.add(ScoringCategoryEnum.XLHHY);
                } else {
                    categoryList.add(ScoringCategoryEnum.SHCY);
                }
                meeting.setCategory(MeetingConverter.categoryToString(categoryList));

                // 更新人更新时间
                meeting.setUpdateBy(SecurityUtils.getUserId());
                meeting.setUpdateTime(LocalDateTime.now());

                // 更新评分记录表数据
                for (String participantId : participantIds) {
                    // 归属多个部门需要拆分成多条记录
                    categoryList.forEach(category -> {
                        UpdateWrapper<ScoringRecord> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("activity_id", meeting.getId())
                                .eq("scoring_type", ScoreTypeEnum.MEETING)
                                .eq("member_id", participantId);
                        ScoringRecord scoringRecord = new ScoringRecord();
                        scoringRecord.setCategory(category.getValue());
                        scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                        scoringRecord.setUpdateTime(LocalDateTime.now());
                        scoringRecordMapper.update(scoringRecord, updateWrapper);
                    });
                }

            });
            // 批量更新表格数据
            this.updateBatchById(meetings);
        }

        return true;
    }

}