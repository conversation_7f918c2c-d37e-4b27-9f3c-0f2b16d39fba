package com.youlai.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.common.model.Option;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.model.vo.UserInfoVO;
import com.youlai.boot.system.model.vo.UserPageVO;
import com.youlai.boot.system.model.vo.UserProfileVO;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.form.UserForm;
import com.youlai.boot.system.model.dto.UserImportDTO;
import com.youlai.boot.system.model.form.UserProfileForm;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.AfterMapping;
import org.mapstruct.MappingTarget;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户对象转换器
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Mapper(componentModel = "spring")
public interface UserConverter {

    UserPageVO toPageVo(UserBO bo);

    Page<UserPageVO> toPageVo(Page<UserBO> bo);

    UserForm toForm(User entity);

    @InheritInverseConfiguration(name = "toForm")
    User toEntity(UserForm entity);

    @Mappings({
            @Mapping(target = "userId", source = "id")
    })
    UserInfoVO toUserInfoVo(User entity);

    User toEntity(UserImportDTO vo);

    UserProfileVO toProfileVO(UserBO bo);

    @AfterMapping
    default void afterToProfileVO(UserBO bo, @MappingTarget UserProfileVO vo) {
        // 处理角色列表
        if (bo != null && bo.getRolesStr() != null) {
            String[] roleStr = bo.getRolesStr().split(",");
            List<UserProfileVO.RoleInfo> roles = Arrays.stream(roleStr)
                    .map(s -> {
                        String[] split = s.split(":");
                        UserProfileVO.RoleInfo roleInfo = new UserProfileVO.RoleInfo();
                        roleInfo.setId(Long.parseLong(split[0]));
                        roleInfo.setCode(split[1]);
                        roleInfo.setName(split[2]);
                        return roleInfo;
                    })
                    .collect(Collectors.toList());
            vo.setRoles(roles);
        }
    }

    User toEntity(UserProfileForm formData);

    @Mappings({
            @Mapping(target = "label", source = "nickname"),
            @Mapping(target = "value", source = "id")
    })
    Option<String> toOption(User entity);

    List<Option<String>> toOptions(List<User> list);
}
