package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 活动类型枚举
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
public enum ScoreTypeEnum implements IBaseEnum<String> {

    MEETING("MEETING", "参加会议"),
    ACTIVITY("ACTIVITY", "参加活动"),
    KEY_WORK("KEY_WORK", "年度重点工作"),
    ENVIRONMENT("ENVIRONMENT", "营商环境问题报送"),
    OPINION("OPINION", "意见建议");

    private final String value;

    private final String label;

    ScoreTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}