package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 意见审核状态枚举
 */
@Getter
public enum OpinionAuditStatus implements IBaseEnum<String> {

    SUBMITTED("SUBMITTED", "已提交"),
    ADOPTED("ADOPTED", "已采用"),
    REJECTED("REJECTED", "不予采用");

    private final String value;

    private final String label;

    OpinionAuditStatus(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
