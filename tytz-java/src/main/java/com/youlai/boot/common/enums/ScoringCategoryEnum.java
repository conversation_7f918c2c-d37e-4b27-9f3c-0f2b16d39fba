package com.youlai.boot.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 履职得分归类（所属商会）
 * 
 * <AUTHOR>
 * @since 2025-06-26
 */
@Getter
public enum ScoringCategoryEnum implements IBaseEnum<String> {
    SHCY("SHCY", "总商会"),
    XLHHY("XLHHY", "新联会");

    @EnumValue
    private final String value;

    private final String label;

    ScoringCategoryEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static ScoringCategoryEnum getEnumByValue(String value) {
        for (ScoringCategoryEnum scoringCategoryEnum : ScoringCategoryEnum.values()) {
            if (scoringCategoryEnum.getValue().equals(value)) {
                return scoringCategoryEnum;
            }
        }
        return null;
    }
}
