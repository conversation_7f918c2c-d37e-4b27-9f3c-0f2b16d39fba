<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getAuthCaptcha, authLogin } from '@/api/login'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'
import { getUserInfo } from '@/api/user'
import { useNewUserStoreHook } from '@/store/modules/user'
import { ScoringCategoryDicts } from '@/dicts/ScoringCategoryDicts'
import { useMessage } from 'wot-design-uni'
const message = useMessage('wd-message-box-slot')

const userStore = useNewUserStoreHook()

defineOptions({
  name: 'Login',
})

const props = defineProps<{
  redirectTo?: ReLaunchOptions['url']
}>()

const captchaKey = ref('')
const captchaBase64 = ref('')

const formData = reactive({
  username: '',
  password: '',
  captchaCode: '',
})

/** 可选的归属部门 */
const rolesOptions = computed(() => {
  return ScoringCategoryDicts.filter((item) => {
    return (userStore.userInfo.roles || []).some((role) => role.code === item.value)
  })
})

/**
 * 获取验证码
 */
function getCaptcha() {
  getAuthCaptcha().then((res) => {
    captchaKey.value = res.captchaKey
    captchaBase64.value = res.captchaBase64
  })
}
getCaptcha()

/** 登录成功跳转页面 */
const handleLoginSuccess = () => {
  if (props.redirectTo) {
    useNavigate(props.redirectTo, {}, 'redirectTo')
  } else {
    uni.navigateBack({
      fail: () => {
        uni.switchTab({ url: '/pages/index/index' })
      },
    })
  }
}

/** 校验一下登录参数 */
const validateFormData = () => {
  if (!formData.username) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none',
    })
    return false
  }
  if (!formData.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none',
    })
    return false
  }
  if (!formData.captchaCode) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none',
    })
    return false
  }
  return true
}

/** 点击登录 */
async function handleLogin() {
  const params = {
    username: formData.username,
    password: formData.password,
    captchaKey: captchaKey.value,
    captchaCode: formData.captchaCode,
  }
  // 校验一下参数
  if (!validateFormData()) return false

  uni.showLoading({
    title: '登录中...',
  })
  try {
    const res = await authLogin(params)
    uni.setStorageSync(TOKEN_TYPE, res.tokenType)
    uni.setStorageSync(ACCESS_TOKEN, res.accessToken)
    uni.setStorageSync(REFRESH_TOKEN, res.refreshToken)
    const user = await getUserInfo()
    userStore.setUserInfo(user)
    // 判断一下是否属于多个商会，如果是的话，需要选择商会
    if (rolesOptions.value.length > 1) {
      uni.hideLoading()
      message
        .alert({
          title: '提示',
          showCancelButton: false,
          closeOnClickModal: false,
          confirmButtonText: '确定',
          beforeConfirm: ({ resolve }) => {
            if (!selectedRoleId.value) {
              uni.showToast({
                title: '请选择商会',
                icon: 'none',
              })
              resolve(false)
            } else {
              resolve(true)
            }
          },
        })
        .then(() => {
          const selectedRole = userStore.userInfo.roles.find(
            (role) => role.code === selectedRoleId.value,
          )
          userStore.setRole(selectedRole)
          handleLoginSuccess()
        })
    } else {
      if (rolesOptions.value.length === 1) {
        const selectedRole = userStore.userInfo.roles.find(
          (role) => role.code === rolesOptions.value[0].value,
        )
        userStore.setRole(selectedRole)
      }
      handleLoginSuccess()
      uni.hideLoading()
    }
  } catch (error) {
    getCaptcha()
  } finally {
    // uni.hideLoading()
  }
}

/** 当前选中的身份 */
const selectedRoleId = ref('')

/** 切换选中的身份 */
const handleChangeRoles = (e) => {
  const selectedRole = userStore.userInfo.roles.find((role) => role.code === e.value)
  if (!selectedRole) {
    uni.showToast({
      title: '身份设置失败',
      icon: 'error',
    })
  }
}
</script>

<template>
  <PageContainer theme="login">
    <view class="p-20px flex-1">
      <view class="text-#222222 text-48rpx mt-18vh">你好，</view>
      <view class="text-#222222 text-48rpx">天涯区智慧统战平台</view>
      <view class="mt-40rpx">
        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="user" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.username"
            class="flex-1 h-full"
            type="text"
            placeholder="请输入用户手机号"
            placeholder-class="text-#999999"
          />
        </view>

        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="lock-on" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.password"
            class="flex-1 h-full"
            password
            placeholder="请输入密码"
            placeholder-class="text-#999999"
          />
        </view>
        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="secured" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.captchaCode"
            class="flex-1 h-full"
            type="text"
            placeholder="请输入验证码"
            placeholder-class="text-#999999"
          />
          <view class="w-230rpx h-90rpx flex items-center justify-center">
            <image :src="captchaBase64" mode="widthFix" @click="getCaptcha" />
          </view>
        </view>
      </view>
      <view
        class="w-full h-90rpx flex items-center justify-center mt-40rpx bg-#EF4142 text-white"
        @click="handleLogin"
      >
        登录
      </view>

      <wd-message-box selector="wd-message-box-slot">
        <view style="padding: 20rpx 0 20rpx">
          <view style="padding-bottom: 20rpx">您的账号归属多个商会，请选择其中的某个商会登录</view>
          <wd-radio-group
            v-model="selectedRoleId"
            class="custom-radio-group"
            shape="check"
            @change="handleChangeRoles"
          >
            <wd-radio
              v-for="role in rolesOptions"
              :key="role.value"
              :value="role.value"
              class="custom-radio"
            >
              {{ role.label }}
            </wd-radio>
          </wd-radio-group>
        </view>
      </wd-message-box>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
.input-box {
  width: 100%;
  height: 90rpx;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 10rpx;
  + .input-box {
    margin-top: 20rpx;
  }
}
</style>
