import store from '@/store'
import { defineStore } from 'pinia'
import { ACCESS_TOKEN, REFRESH_TOKEN, TOKEN_TYPE } from '@/const/storage'

/**
 * UserProfileVO
 */
export interface UserProfileVO {
  /**
   * 头像URL
   */
  avatar?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 部门名称
   */
  deptName?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 性别
   */
  gender?: number
  /**
   * 用户ID
   */
  id?: number
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 用户昵称
   */
  nickname?: string
  /**
   * 角色名称
   */
  roleNames?: string
  /**
   * 用户名
   */
  username?: string

  /** 公司名称 */
  company?: string

  /** 用户身份 */
  roles?: Role[]
}

export interface Role {
  id: string
  code: string
  name: string
}

const useNewUserStore = defineStore(
  'newUser',
  () => {
    /**
     * 用户信息
     */
    const userInfo = ref<UserProfileVO>({})
    const role = ref<Role>({
      id: '',
      code: '',
      name: '',
    })

    /**
     * 设置用户信息
     * @param data
     */
    const setUserInfo = (data: UserProfileVO) => {
      userInfo.value = data
    }

    /** 设置用户身份 */
    const setRole = (data: Role) => {
      role.value.id = data.id
      role.value.code = data.code
      role.value.name = data.name
    }

    const clearUserInfo = () => {
      userInfo.value = {}
      uni.removeStorageSync(ACCESS_TOKEN)
      uni.removeStorageSync(TOKEN_TYPE)
      uni.removeStorageSync(REFRESH_TOKEN)
      role.value = {
        id: '',
        code: '',
        name: '',
      }
    }

    const isLogin = computed(() => {
      return JSON.stringify(userInfo.value) !== '{}'
    })

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      role,
      setRole,
      isLogin,
    }
  },
  {
    persist: true,
  },
)

export function useNewUserStoreHook() {
  return useNewUserStore(store)
}
