<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { getUserInfo, refreshToken } from '@/api/user'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'
import { useNewUserStoreHook } from '@/store/modules/user'
import { ScoringCategoryDicts } from './dicts/ScoringCategoryDicts'

const userStore = useNewUserStoreHook()

/** 可选的归属部门 */
const rolesOptions = computed(() => {
  return ScoringCategoryDicts.filter((item) => {
    return (userStore.userInfo.roles || []).some((role) => role.code === item.value)
  })
})

if (uni.getStorageSync(REFRESH_TOKEN)) {
  refreshToken()
    .then((res) => {
      uni.setStorageSync(TOKEN_TYPE, res.tokenType)
      uni.setStorageSync(ACCESS_TOKEN, res.accessToken)
      uni.setStorageSync(REFRESH_TOKEN, res.refreshToken)
      return getUserInfo()
    })
    .then((res) => {
      userStore.setUserInfo(res)
      // 提示并强制用户去个人中心选择一下身份
      nextTick(() => {
        if (rolesOptions.value.length > 1) {
          // 如果不在登录页面/个人中心页面则提示去个人中心选择身份
          const currentPages = getCurrentPages()
          if (currentPages.length > 0) {
            const currentPage = currentPages.pop()
            if (
              currentPage &&
              currentPage.route !== 'pages/login/index' &&
              currentPage.route !== 'pages/person/index'
            ) {
              uni.showModal({
                title: '提示',
                content: '您的账号归属多个商会，请先选择其中的某个商会登录',
                showCancel: false,
                complete: () => {
                  uni.switchTab({ url: '/pages/person/index' })
                },
              })
            }
          }
        } else {
          if (rolesOptions.value.length === 1) {
            const selectedRole = userStore.userInfo.roles.find(
              (role) => role.code === rolesOptions.value[0].value,
            )
            userStore.setRole(selectedRole)
          }
          uni.switchTab({ url: '/pages/index/index' })
        }
      })
    })
}

onLaunch(() => {
  // console.log('App Launch')
  autoUpdate()
})
onShow(() => {
  // console.log('App Show')
})
onHide(() => {
  // console.log('App Hide')
})

const autoUpdate = () => {
  // 获取小程序更新机制兼容
  if (wx.canIUse('getUpdateManager')) {
    const updateManager = wx.getUpdateManager()
    // 1. 检查小程序是否有新版本发布
    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      if (res.hasUpdate) {
        // 检测到新版本，需要更新，给出提示
        wx.showModal({
          title: '更新提示',
          content: '检测到新版本，是否下载新版本并重启小程序？',
          success: function (res) {
            if (res.confirm) {
              // 2. 用户确定下载更新小程序，小程序下载及更新静默进行
              downLoadAndUpdate(updateManager)
            } else if (res.cancel) {
              // 用户点击取消按钮的处理，如果需要强制更新，则给出二次弹窗，如果不需要，则这里的代码都可以删掉了
              wx.showModal({
                title: '温馨提示',
                content: '本次版本更新涉及到新的功能添加，旧版本可能无法正常访问',
                showCancel: false, // 隐藏取消按钮
                confirmText: '确定更新', // 只保留确定更新按钮
                success: function (res) {
                  if (res.confirm) {
                    // 下载新版本，并重新应用
                    downLoadAndUpdate(updateManager)
                  }
                },
              })
            }
          },
        })
      }
    })
  } else {
    // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
    wx.showModal({
      title: '提示',
      content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
    })
  }
}
/**
 * 下载小程序新版本并重启应用
 */
const downLoadAndUpdate = (updateManager) => {
  uni.showLoading()
  // 静默下载更新小程序新版本
  updateManager.onUpdateReady(function () {
    wx.hideLoading()
    // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
    updateManager.applyUpdate()
  })
  updateManager.onUpdateFailed(function () {
    // 新的版本下载失败
    wx.showModal({
      title: '更新小程序失败',
      content: '请重启小程序',
    })
    uni.hideLoading()
  })
}
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}
view {
  box-sizing: border-box;
}
swiper,
scroll-view {
  box-sizing: border-box;
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
