<script setup lang="ts">
import { ref, computed } from "vue";
import { appointmentsPageVO } from "@/api/appointment/index";
import dayjs from "dayjs";
import { statusEnum, statusStyleEnum } from "../enum";
import { ElMessage } from "element-plus";

interface AppointmentDetailProps {
  visible: boolean;
  data?: appointmentsPageVO;
}

const props = defineProps<AppointmentDetailProps>();
const emit = defineEmits(["update:visible", "confirm"]);

const form = ref({
  handleStatus: "PASS",
  handleComment: "",
});

const title = computed(() => {
  if (!props.data) return "约见详情";
  switch (props.data.handleStatus) {
    case "PASS":
    case "WAIT":
      return "约见详情";
    case "FINISH":
      return "约见反馈";
    default:
      return "约见详情";
  }
});

const handleConfirm = () => {
  if (!props.data) return;
  if (form.value.handleStatus === "REJECT") {
    if (form.value.handleComment === "") {
      ElMessage.error("请填写处理意见");
      return;
    }
  }
  emit("confirm", {
    ...form.value,
    id: props.data.id,
  });
};
</script>

<template>
  <div>
    <el-dialog
      :title="title"
      :model-value="visible"
      center
      width="500px"
      @update:model-value="(val: any) => emit('update:visible', val)"
    >
      <div v-if="data" class="appointment-detail">
        <div class="info-item">
          <span class="label">约见人名称:</span>
          <span>{{ data.appointmentName }}</span>
        </div>
        <div class="info-item">
          <span class="label">所属单位:</span>
          <span>{{ data.appointmentUnit }}</span>
        </div>
        <div class="info-item">
          <span class="label">联系方式:</span>
          <span>{{ data.appointmentContact }}</span>
        </div>
        <div class="info-item">
          <span class="label">被约见部委:</span>
          <span>{{ data.appointmentDepartment }}</span>
        </div>
        <div class="info-item">
          <span class="label">约见原因:</span>
          <span>{{ data.appointmentReason }}</span>
        </div>
        <div class="info-item">
          <span class="label">约见时间:</span>
          <span class="mr-1 text-color-#349DFF">
            {{ dayjs(data.appointmentStartTime).format("YYYY/MM/DD HH:mm") }}
          </span>
          至
          <span class="ml-1 text-color-#349DFF">
            {{ dayjs(data.appointmentEndTime).format("YYYY/MM/DD HH:mm") }}
          </span>
        </div>
        <div v-if="data.appointmentStatus !== statusStyleEnum.WAIT.value" class="info-item">
          <span class="label">约见状态:</span>
          <div
            class="pointer"
            :style="{
              backgroundColor:
                statusStyleEnum[data.appointmentStatus as keyof typeof statusEnum]?.color,
            }"
          ></div>
          <span
            :style="{
              color: statusStyleEnum[data.appointmentStatus as keyof typeof statusEnum]?.color,
            }"
          >
            {{ statusEnum[data.appointmentStatus as keyof typeof statusEnum] }}
          </span>
        </div>
        <!-- 待约见状态显示选择框和原因输入 -->
        <template v-if="data.appointmentStatus == statusStyleEnum.WAIT.value">
          <div class="radio-group">
            <span class="label">
              <span class="required">*</span>
              约见处理:
            </span>
            <el-radio-group v-model="form.handleStatus">
              <el-radio value="PASS">予以约见</el-radio>
              <el-radio value="REJECT">不予约见</el-radio>
            </el-radio-group>
          </div>
          <div v-if="form.handleStatus === 'REJECT'" class="reason-input">
            <span class="label">
              <span class="required">*</span>
              约见意见:
            </span>
            <el-input
              v-model="form.handleComment"
              type="textarea"
              :rows="3"
              placeholder="请在此处输入约见意见..."
            />
          </div>
        </template>
        <!--不与约见处理 -->
        <template v-if="data.appointmentStatus == statusStyleEnum.REJECT.value">
          <div class="info-item">
            <span class="label">处理意见:</span>
            <span>不予约见</span>
          </div>
        </template>
      </div>

      <template #footer>
        <el-button @click="emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.appointment-detail {
  .info-item {
    display: flex;
    margin-bottom: 15px;

    .label {
      width: 100px;
      color: #606266;
    }
  }

  .el-radio-group {
    margin-left: 25px;
  }

  .reason-input {
    margin-top: 20px;

    .label {
      display: block;
      margin-bottom: 10px;
      color: #606266;
    }

    .el-input {
      width: 100%;
    }
  }
  .required {
    color: #f56c6c;
  }
}
.pointer {
  width: 8px;
  height: 8px;
  margin-top: 6px;
  margin-right: 4px;
  border-radius: 50%;
}
</style>
../enum
